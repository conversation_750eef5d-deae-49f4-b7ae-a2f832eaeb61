"""
Main scraping engine using Crawl4AI with advanced features
"""
import asyncio
import time
import random
from typing import List, Dict, Optional, Set, Tuple
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, asdict
from loguru import logger
import aiofiles
import json

try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
except ImportError:
    logger.error("Crawl4AI not installed. Please install with: pip install crawl4ai")
    raise

from extractors import ContactExtractor, ExtractedContact, ContentProcessor
from config import AppConfig, ScrapingMode


@dataclass
class ScrapingResult:
    """Result of scraping a single URL"""
    url: str
    success: bool
    contacts: Optional[ExtractedContact] = None
    error: Optional[str] = None
    response_time: float = 0.0
    status_code: Optional[int] = None
    content_length: int = 0
    timestamp: float = 0.0


class ScrapingEngine:
    """High-performance scraping engine with Crawl4AI"""
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.extractor = ContactExtractor(config.patterns)
        self.processor = ContentProcessor()
        self.crawler = None
        self.results: List[ScrapingResult] = []
        self.processed_urls: Set[str] = set()
        
        # Performance tracking
        self.start_time = 0.0
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.stop()
    
    async def start(self):
        """Initialize the scraping engine"""
        logger.info("Starting scraping engine...")

        # Configure browser settings - use minimal config that works
        browser_config = BrowserConfig(
            headless=self.config.scraping.headless,
            verbose=False  # Reduce verbosity to avoid issues
        )

        # Initialize crawler with working config
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.start()

        self.start_time = time.time()
        logger.info("Scraping engine started successfully")
    
    async def stop(self):
        """Clean up resources"""
        if self.crawler:
            await self.crawler.stop()
        
        # Log final statistics
        elapsed_time = time.time() - self.start_time
        logger.info(f"Scraping completed in {elapsed_time:.2f} seconds")
        logger.info(f"Total requests: {self.total_requests}")
        logger.info(f"Successful: {self.successful_requests}")
        logger.info(f"Failed: {self.failed_requests}")
        logger.info(f"Success rate: {(self.successful_requests/max(self.total_requests, 1)*100):.1f}%")
    
    async def scrape_url(self, url: str, retries: int = None) -> ScrapingResult:
        """Scrape a single URL with retry logic"""
        if retries is None:
            retries = self.config.scraping.max_retries
        
        start_time = time.time()
        self.total_requests += 1
        
        for attempt in range(retries + 1):
            try:
                logger.info(f"Scraping {url} (attempt {attempt + 1}/{retries + 1})")
                
                # Add random delay for anti-detection
                if self.config.scraping.random_delays and attempt > 0:
                    delay = random.uniform(0.5, 2.0) * self.config.scraping.retry_delay
                    await asyncio.sleep(delay)
                
                # Configure crawling parameters
                run_config = self._get_run_config()
                
                # Perform the crawl
                result = await self.crawler.arun(url=url, config=run_config)
                
                if result.success:
                    # Extract contact information
                    contacts = await self._extract_contacts(result, url)
                    
                    response_time = time.time() - start_time
                    self.successful_requests += 1
                    
                    scraping_result = ScrapingResult(
                        url=url,
                        success=True,
                        contacts=contacts,
                        response_time=response_time,
                        status_code=getattr(result, 'status_code', 200),
                        content_length=len(result.html) if result.html else 0,
                        timestamp=time.time()
                    )
                    
                    self.results.append(scraping_result)
                    self.processed_urls.add(url)
                    
                    logger.success(f"Successfully scraped {url} in {response_time:.2f}s")
                    return scraping_result
                
                else:
                    logger.warning(f"Crawl failed for {url}: {getattr(result, 'error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"Error scraping {url} (attempt {attempt + 1}): {str(e)}")
                
                if attempt < retries:
                    await asyncio.sleep(self.config.scraping.retry_delay)
                    continue
        
        # All attempts failed
        response_time = time.time() - start_time
        self.failed_requests += 1
        
        scraping_result = ScrapingResult(
            url=url,
            success=False,
            error="Max retries exceeded",
            response_time=response_time,
            timestamp=time.time()
        )
        
        self.results.append(scraping_result)
        logger.error(f"Failed to scrape {url} after {retries + 1} attempts")
        return scraping_result
    
    async def scrape_urls(self, urls: List[str], progress_callback=None) -> List[ScrapingResult]:
        """Scrape multiple URLs with concurrency control"""
        logger.info(f"Starting to scrape {len(urls)} URLs with max concurrency: {self.config.scraping.max_concurrent}")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(self.config.scraping.max_concurrent)
        
        async def scrape_with_semaphore(url: str, index: int) -> ScrapingResult:
            async with semaphore:
                # Add delay between requests
                if index > 0:
                    await asyncio.sleep(self.config.scraping.request_delay)
                
                result = await self.scrape_url(url)
                
                if progress_callback:
                    await progress_callback(index + 1, len(urls), result)
                
                return result
        
        # Create tasks for all URLs
        tasks = [scrape_with_semaphore(url, i) for i, url in enumerate(urls)]
        
        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed for {urls[i]}: {str(result)}")
                final_results.append(ScrapingResult(
                    url=urls[i],
                    success=False,
                    error=str(result),
                    timestamp=time.time()
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    def _get_run_config(self) -> CrawlerRunConfig:
        """Get crawler run configuration based on scraping mode"""
        # Use minimal working configuration
        config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,  # Bypass cache for fresh results
            word_count_threshold=10  # Lower threshold
        )

        return config
    
    async def _extract_contacts(self, crawl_result, url: str) -> ExtractedContact:
        """Extract contact information from crawl result"""
        try:
            # Get content
            html = crawl_result.html or ""
            text = crawl_result.cleaned_html or ""
            markdown = crawl_result.markdown or ""
            
            # Combine text sources
            combined_text = f"{text} {markdown}"
            
            # Extract contact information
            contacts = self.extractor.extract_all(
                text=combined_text,
                html=html,
                base_url=url
            )
            
            logger.debug(f"Extracted from {url}: {len(contacts.emails)} emails, "
                        f"{sum(len(links) for links in contacts.social_media.values())} social links, "
                        f"{len(contacts.phone_numbers)} phone numbers")
            
            return contacts
            
        except Exception as e:
            logger.error(f"Error extracting contacts from {url}: {str(e)}")
            return ExtractedContact(
                emails=set(),
                social_media={platform: set() for platform in self.config.patterns.social_media_patterns.keys()},
                phone_numbers=set(),
                urls=set(),
                source_url=url
            )
    
    def get_statistics(self) -> Dict:
        """Get scraping statistics"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        
        # Aggregate contact statistics
        total_emails = sum(len(r.contacts.emails) if r.contacts else 0 for r in self.results)
        total_social = sum(
            sum(len(links) for links in r.contacts.social_media.values()) if r.contacts else 0
            for r in self.results
        )
        total_phones = sum(len(r.contacts.phone_numbers) if r.contacts else 0 for r in self.results)
        
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": (self.successful_requests / max(self.total_requests, 1)) * 100,
            "elapsed_time": elapsed_time,
            "requests_per_second": self.total_requests / max(elapsed_time, 1),
            "total_emails_found": total_emails,
            "total_social_links_found": total_social,
            "total_phone_numbers_found": total_phones,
            "unique_domains_processed": len(set(urlparse(r.url).netloc for r in self.results))
        }
    
    async def save_results(self, filename: str = None) -> str:
        """Save results to file"""
        if not filename:
            timestamp = int(time.time())
            filename = f"scraping_results_{timestamp}.json"
        
        # Prepare data for serialization
        data = {
            "metadata": {
                "timestamp": time.time(),
                "total_urls": len(self.results),
                "statistics": self.get_statistics()
            },
            "results": [
                {
                    "url": r.url,
                    "success": r.success,
                    "contacts": r.contacts.to_dict() if r.contacts else None,
                    "error": r.error,
                    "response_time": r.response_time,
                    "status_code": r.status_code,
                    "content_length": r.content_length,
                    "timestamp": r.timestamp
                }
                for r in self.results
            ]
        }
        
        async with aiofiles.open(filename, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(data, indent=2, ensure_ascii=False))
        
        logger.info(f"Results saved to {filename}")
        return filename

#!/usr/bin/env python3
"""
Basic test of extraction functionality without complex scraping
"""
import requests
import pandas as pd
import json
from urllib.parse import urljoin
import time

# Import our extraction components
from extractors import ContactExtractor
from config import get_config


def simple_http_scrape(url, timeout=10):
    """Simple HTTP request to get page content"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout, allow_redirects=True)
        response.raise_for_status()
        
        return {
            'success': True,
            'html': response.text,
            'status_code': response.status_code,
            'url': response.url
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'html': '',
            'status_code': None,
            'url': url
        }


def test_extraction_patterns():
    """Test our extraction patterns with sample data"""
    print("🔍 Testing Extraction Patterns...")
    
    # Sample HTML content that should contain contacts
    sample_html = """
    <html>
    <head><title>Coffee Shop</title></head>
    <body>
        <div class="contact-info">
            <h2>Contact Us</h2>
            <p>Email us at: info@coffeeshop.<NAME_EMAIL></p>
            <p>Call us: (************* or ******-987-6543</p>
            <p>Follow us on social media:</p>
            <a href="https://facebook.com/coffeeshop">Facebook</a>
            <a href="https://instagram.com/coffeeshop">Instagram</a>
            <a href="https://twitter.com/coffeeshop">Twitter</a>
            <p>Alternative contact: support [at] coffeeshop [dot] com</p>
        </div>
        <footer>
            <p>Visit us at our location <NAME_EMAIL></p>
            <p>Phone: ************</p>
        </footer>
    </body>
    </html>
    """
    
    sample_text = """
    Welcome to our coffee shop!
    Contact: <EMAIL>
    Phone: (*************
    Social: https://linkedin.com/company/coffeeshop
    """
    
    config = get_config()
    extractor = ContactExtractor(config.patterns)
    
    # Extract contacts
    contacts = extractor.extract_all(sample_text, sample_html, "https://coffeeshop.com")
    
    print(f"📧 Emails found: {list(contacts.emails)}")
    print(f"📞 Phone numbers found: {list(contacts.phone_numbers)}")
    print(f"📱 Social media links:")
    for platform, links in contacts.social_media.items():
        if links:
            print(f"   {platform}: {list(links)}")
    
    # Count total
    total_contacts = (
        len(contacts.emails) + 
        sum(len(links) for links in contacts.social_media.values()) + 
        len(contacts.phone_numbers)
    )
    
    print(f"✅ Total contacts extracted: {total_contacts}")
    return total_contacts > 0


def test_real_websites():
    """Test with real websites using simple HTTP requests"""
    print("\n🌐 Testing Real Websites...")
    
    # Read test data
    try:
        df = pd.read_csv('test_data.csv')
        urls = df['website_url'].dropna().tolist()[:5]  # Test first 5 URLs
        print(f"📁 Testing {len(urls)} URLs with simple HTTP requests")
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False
    
    config = get_config()
    extractor = ContactExtractor(config.patterns)
    
    results = []
    successful = 0
    
    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] Testing {url}")
        
        # Simple HTTP request
        response = simple_http_scrape(url)
        
        if response['success']:
            # Extract contacts
            contacts = extractor.extract_all(
                text="",  # We don't have cleaned text, just HTML
                html=response['html'],
                base_url=response['url']
            )
            
            # Count findings
            email_count = len(contacts.emails)
            social_count = sum(len(links) for links in contacts.social_media.values())
            phone_count = len(contacts.phone_numbers)
            total_contacts = email_count + social_count + phone_count
            
            print(f"   ✅ Success! Status: {response['status_code']}")
            print(f"   📊 Found {total_contacts} contacts:")
            
            if contacts.emails:
                print(f"      📧 Emails: {', '.join(list(contacts.emails)[:3])}")
            if any(contacts.social_media.values()):
                social_found = []
                for platform, links in contacts.social_media.items():
                    if links:
                        social_found.extend([f"{platform}" for _ in links])
                if social_found:
                    print(f"      📱 Social: {', '.join(social_found[:3])}")
            if contacts.phone_numbers:
                print(f"      📞 Phones: {', '.join(list(contacts.phone_numbers)[:2])}")
            
            results.append({
                'url': url,
                'success': True,
                'contacts': contacts.to_dict(),
                'total_contacts': total_contacts,
                'status_code': response['status_code']
            })
            
            if total_contacts > 0:
                successful += 1
        else:
            print(f"   ❌ Failed: {response['error']}")
            results.append({
                'url': url,
                'success': False,
                'error': response['error']
            })
        
        # Small delay between requests
        time.sleep(1)
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print(f"   ✅ Successful requests: {len([r for r in results if r.get('success', False)])}/{len(results)}")
    print(f"   🎯 Found contacts: {successful}/{len(results)}")
    
    if successful > 0:
        total_contacts_found = sum(r.get('total_contacts', 0) for r in results if r.get('success', False))
        print(f"   📧 Total contacts found: {total_contacts_found}")
        
        # Save results
        output_file = "basic_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_urls': len(results),
                    'successful_requests': len([r for r in results if r.get('success', False)]),
                    'found_contacts': successful,
                    'total_contacts': total_contacts_found
                },
                'results': results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"   📁 Results saved to: {output_file}")
        return True
    else:
        print("   ⚠️  No contacts found in any website")
        return False


def main():
    """Run basic tests"""
    print("🚀 Basic Contact Scraper Test")
    print("=" * 50)
    
    # Test 1: Pattern extraction
    print("Test 1: Pattern Extraction")
    pattern_test = test_extraction_patterns()
    
    if not pattern_test:
        print("❌ Pattern extraction failed!")
        return False
    
    # Test 2: Real websites
    print("\nTest 2: Real Website Testing")
    website_test = test_real_websites()
    
    print("\n" + "=" * 50)
    if pattern_test and website_test:
        print("🎉 All tests passed! The scraper core functionality works!")
        return True
    else:
        print("⚠️  Some tests failed, but pattern extraction works.")
        return pattern_test


if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)

#!/usr/bin/env python3
"""
Minimal Crawl4AI test to diagnose issues
"""
import asyncio
import sys

print("🔧 Crawl4AI Diagnostic Test")
print("=" * 40)

# Test 1: Import
print("1. Testing imports...")
try:
    from crawl4ai import AsyncWebCrawler
    print("   ✅ AsyncWebCrawler imported")
except ImportError as e:
    print(f"   ❌ Import failed: {e}")
    sys.exit(1)

try:
    from crawl4ai import BrowserConfig, CrawlerRunConfig
    print("   ✅ Config classes imported")
except ImportError as e:
    print(f"   ❌ Config import failed: {e}")

# Test 2: Basic crawler creation
print("\n2. Testing crawler creation...")
try:
    crawler = AsyncWebCrawler()
    print("   ✅ Crawler created successfully")
except Exception as e:
    print(f"   ❌ Crawler creation failed: {e}")
    sys.exit(1)

# Test 3: Simple crawl
async def test_simple_crawl():
    print("\n3. Testing simple crawl...")
    
    # Test with a simple, reliable URL
    test_url = "https://httpbin.org/html"
    
    try:
        async with AsyncWebCrawler() as crawler:
            print(f"   🕷️  Crawling {test_url}...")
            
            result = await crawler.arun(url=test_url)
            
            if result.success:
                print("   ✅ Crawl successful!")
                print(f"   📄 Content length: {len(result.html) if result.html else 0}")
                print(f"   📝 Text length: {len(result.cleaned_html) if result.cleaned_html else 0}")
                
                # Show first 200 chars of content
                if result.html:
                    preview = result.html[:200].replace('\n', ' ')
                    print(f"   👀 Preview: {preview}...")
                
                return True
            else:
                print(f"   ❌ Crawl failed: {getattr(result, 'error', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"   ❌ Exception during crawl: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Test 4: Multiple URLs
async def test_multiple_urls():
    print("\n4. Testing multiple URLs...")
    
    test_urls = [
        "https://httpbin.org/html",
        "https://example.com",
        "https://httpbin.org/json"
    ]
    
    try:
        async with AsyncWebCrawler() as crawler:
            results = []
            
            for i, url in enumerate(test_urls, 1):
                print(f"   [{i}/{len(test_urls)}] Crawling {url}...")
                
                try:
                    result = await crawler.arun(url=url)
                    
                    if result.success:
                        print(f"      ✅ Success - {len(result.html) if result.html else 0} chars")
                        results.append(True)
                    else:
                        print(f"      ❌ Failed: {getattr(result, 'error', 'Unknown')}")
                        results.append(False)
                        
                except Exception as e:
                    print(f"      ❌ Exception: {str(e)}")
                    results.append(False)
                
                # Small delay
                await asyncio.sleep(0.5)
            
            success_count = sum(results)
            print(f"   📊 Results: {success_count}/{len(test_urls)} successful")
            
            return success_count > 0
            
    except Exception as e:
        print(f"   ❌ Multiple URL test failed: {str(e)}")
        return False

# Test 5: Coffee shop URL
async def test_coffee_shop():
    print("\n5. Testing actual coffee shop URL...")
    
    # Use one of the URLs from the CSV
    coffee_url = "https://www.novariacoffee.com/"
    
    try:
        async with AsyncWebCrawler() as crawler:
            print(f"   🕷️  Crawling {coffee_url}...")
            
            result = await crawler.arun(url=coffee_url)
            
            if result.success:
                print("   ✅ Coffee shop crawl successful!")
                
                # Look for contact information in the content
                html = result.html or ""
                text = result.cleaned_html or ""
                
                # Simple email search
                import re
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                emails = re.findall(email_pattern, html + " " + text, re.IGNORECASE)
                
                # Simple social media search
                social_patterns = [
                    r'facebook\.com/[A-Za-z0-9._-]+',
                    r'instagram\.com/[A-Za-z0-9._-]+',
                    r'twitter\.com/[A-Za-z0-9._-]+'
                ]
                
                social_links = []
                for pattern in social_patterns:
                    matches = re.findall(pattern, html, re.IGNORECASE)
                    social_links.extend(matches)
                
                print(f"   📧 Emails found: {len(set(emails))}")
                if emails:
                    print(f"      {', '.join(list(set(emails))[:3])}")
                
                print(f"   📱 Social links found: {len(set(social_links))}")
                if social_links:
                    print(f"      {', '.join(list(set(social_links))[:3])}")
                
                return True
            else:
                print(f"   ❌ Coffee shop crawl failed: {getattr(result, 'error', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"   ❌ Coffee shop test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def run_all_tests():
    """Run all diagnostic tests"""
    
    tests = [
        ("Simple Crawl", test_simple_crawl),
        ("Multiple URLs", test_multiple_urls),
        ("Coffee Shop", test_coffee_shop)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 Crawl4AI is working perfectly!")
        print("💡 The issue might be in our scraper configuration.")
    elif passed > 0:
        print("⚠️  Crawl4AI partially working - some configuration issues.")
        print("💡 Try adjusting timeouts, delays, or target URLs.")
    else:
        print("❌ Crawl4AI not working - installation or setup issue.")
        print("💡 Try running: crawl4ai-setup or crawl4ai-doctor")
    
    return passed > 0

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Diagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

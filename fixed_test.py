#!/usr/bin/env python3
"""
Fixed Crawl4AI test based on official documentation
"""
import asyncio
import pandas as pd
import json
import re
from pathlib import Path

# Import Crawl4AI components correctly
from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

# Import our extraction components
from extractors import ContactExtractor
from config import get_config


async def test_basic_crawl4ai():
    """Test basic Crawl4AI functionality"""
    print("🔧 Testing Basic Crawl4AI Functionality")
    print("=" * 50)
    
    # Test with a simple, reliable URL first
    test_url = "https://httpbin.org/html"
    
    try:
        # Use minimal configuration that works
        browser_config = BrowserConfig(headless=True)
        run_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            print(f"🕷️  Crawling {test_url}...")
            
            result = await crawler.arun(url=test_url, config=run_config)
            
            if result.success:
                print("✅ Basic crawl successful!")
                print(f"📄 Content length: {len(result.html) if result.html else 0}")
                print(f"📝 Markdown length: {len(result.markdown) if result.markdown else 0}")
                return True
            else:
                print(f"❌ Basic crawl failed: {getattr(result, 'error', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"❌ Exception in basic test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_coffee_shop_extraction():
    """Test extraction on actual coffee shop URLs"""
    print("\n🕷️  Testing Coffee Shop Extraction")
    print("=" * 50)
    
    # Read test data
    try:
        df = pd.read_csv('test_data.csv')
        urls = df['website_url'].dropna().tolist()[:3]  # Test first 3 URLs
        print(f"📁 Testing {len(urls)} coffee shop URLs:")
        for i, url in enumerate(urls, 1):
            print(f"   {i}. {url}")
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False
    
    # Initialize extractor
    config = get_config()
    extractor = ContactExtractor(config.patterns)
    
    # Configure Crawl4AI
    browser_config = BrowserConfig(headless=True)
    run_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        word_count_threshold=10
    )
    
    results = []
    successful_extractions = 0
    
    print(f"\n🔍 Starting extraction...")
    print("-" * 50)
    
    try:
        async with AsyncWebCrawler(config=browser_config) as crawler:
            
            for i, url in enumerate(urls, 1):
                print(f"[{i}/{len(urls)}] Processing {url}...")
                
                try:
                    # Crawl the page
                    result = await crawler.arun(url=url, config=run_config)
                    
                    if result.success:
                        # Extract contacts using our extractor
                        html = result.html or ""
                        markdown = result.markdown or ""
                        
                        # Use our contact extractor
                        contacts = extractor.extract_all(
                            text=markdown,
                            html=html,
                            base_url=url
                        )
                        
                        # Count findings
                        email_count = len(contacts.emails)
                        social_count = sum(len(links) for links in contacts.social_media.values())
                        phone_count = len(contacts.phone_numbers)
                        total_contacts = email_count + social_count + phone_count
                        
                        print(f"   ✅ Success! Found {total_contacts} contacts:")
                        
                        if contacts.emails:
                            print(f"      📧 Emails: {', '.join(list(contacts.emails)[:3])}")
                        
                        if any(contacts.social_media.values()):
                            social_found = []
                            for platform, links in contacts.social_media.items():
                                if links:
                                    social_found.extend([f"{platform}: {link}" for link in list(links)[:2]])
                            if social_found:
                                print(f"      📱 Social: {', '.join(social_found[:3])}")
                        
                        if contacts.phone_numbers:
                            print(f"      📞 Phones: {', '.join(list(contacts.phone_numbers)[:2])}")
                        
                        results.append({
                            'url': url,
                            'success': True,
                            'contacts': contacts.to_dict(),
                            'total_contacts': total_contacts
                        })
                        
                        if total_contacts > 0:
                            successful_extractions += 1
                    else:
                        print(f"   ❌ Crawl failed: {getattr(result, 'error', 'Unknown error')}")
                        results.append({
                            'url': url,
                            'success': False,
                            'error': getattr(result, 'error', 'Unknown error')
                        })
                
                except Exception as e:
                    print(f"   ❌ Exception: {str(e)}")
                    results.append({
                        'url': url,
                        'success': False,
                        'error': str(e)
                    })
                
                # Small delay between requests
                await asyncio.sleep(1)
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 EXTRACTION RESULTS")
        print("=" * 50)
        
        successful_crawls = len([r for r in results if r.get('success', False)])
        total_contacts_found = sum(r.get('total_contacts', 0) for r in results if r.get('success', False))
        
        print(f"✅ Successful crawls: {successful_crawls}/{len(urls)}")
        print(f"🎯 Found contacts: {successful_extractions}/{len(urls)}")
        print(f"📧 Total contacts found: {total_contacts_found}")
        
        # Save results
        output_file = "fixed_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_urls': len(results),
                    'successful_crawls': successful_crawls,
                    'successful_extractions': successful_extractions,
                    'total_contacts': total_contacts_found
                },
                'results': results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"📁 Results saved to: {output_file}")
        
        if successful_extractions > 0:
            print(f"🎉 Success! Found contacts in {successful_extractions} websites")
            
            # Show detailed findings
            print(f"\n🔍 Detailed Findings:")
            for result in results:
                if result.get('success', False) and result.get('total_contacts', 0) > 0:
                    contacts = result['contacts']
                    print(f"\n   🌐 {result['url']}")
                    
                    if contacts['emails']:
                        print(f"      📧 Emails: {', '.join(contacts['emails'])}")
                    
                    social_found = []
                    for platform, links in contacts['social_media'].items():
                        if links:
                            for link in links:
                                social_found.append(f"{platform}: {link}")
                    
                    if social_found:
                        print(f"      📱 Social Media:")
                        for social in social_found:
                            print(f"         - {social}")
                    
                    if contacts['phone_numbers']:
                        print(f"      📞 Phone Numbers: {', '.join(contacts['phone_numbers'])}")
            
            return True
        else:
            print("⚠️  No contacts found, but crawling worked")
            return successful_crawls > 0
            
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("🚀 Fixed Crawl4AI Contact Scraper Test")
    print("=" * 60)
    
    # Test 1: Basic functionality
    basic_test = await test_basic_crawl4ai()
    
    if not basic_test:
        print("❌ Basic test failed - stopping here")
        return False
    
    # Test 2: Coffee shop extraction
    extraction_test = await test_coffee_shop_extraction()
    
    print("\n" + "=" * 60)
    if basic_test and extraction_test:
        print("🎉 All tests passed! Crawl4AI is working correctly!")
        print("💡 You can now use the scraper with confidence")
        return True
    elif basic_test:
        print("⚠️  Basic crawling works, but extraction needs tuning")
        print("💡 The framework is working - just need to adjust extraction patterns")
        return True
    else:
        print("❌ Tests failed - check configuration")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)

#!/usr/bin/env python3
"""
Simple test of the scraper with minimal configuration
"""
import asyncio
import pandas as pd
import json
from pathlib import Path

# Simple imports to test basic functionality
try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
    print("✅ Crawl4AI imported successfully")
except ImportError as e:
    print(f"❌ Crawl4AI import failed: {e}")
    exit(1)

from extractors import ContactExtractor, ContentProcessor
from config import get_config


async def simple_scrape_test():
    """Simple scraping test with minimal configuration"""
    print("🚀 Simple Scraper Test")
    print("=" * 40)
    
    # Read test URLs
    try:
        df = pd.read_csv('test_data.csv')
        urls = df['website_url'].dropna().tolist()[:3]  # Test only first 3 URLs
        print(f"📁 Testing with {len(urls)} URLs:")
        for i, url in enumerate(urls, 1):
            print(f"   {i}. {url}")
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return
    
    # Initialize extractor
    config = get_config()
    extractor = ContactExtractor(config.patterns)
    
    # Simple browser configuration
    browser_config = BrowserConfig(
        headless=True,
        verbose=False
    )
    
    # Simple crawler configuration
    crawler_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,  # Don't use cache for testing
        word_count_threshold=10
    )
    
    results = []
    
    print(f"\n🕷️  Starting scraping...")
    print("-" * 40)
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        for i, url in enumerate(urls, 1):
            try:
                print(f"[{i}/{len(urls)}] Scraping {url}...")
                
                # Crawl the page
                result = await crawler.arun(url=url, config=crawler_config)
                
                if result.success:
                    # Extract contacts
                    html = result.html or ""
                    text = result.cleaned_html or ""
                    
                    # Use our extractor
                    contacts = extractor.extract_all(text, html, url)
                    
                    # Count findings
                    email_count = len(contacts.emails)
                    social_count = sum(len(links) for links in contacts.social_media.values())
                    phone_count = len(contacts.phone_numbers)
                    total_contacts = email_count + social_count + phone_count
                    
                    print(f"   ✅ Success! Found {total_contacts} contacts:")
                    if contacts.emails:
                        print(f"      📧 Emails: {list(contacts.emails)[:3]}")
                    if any(contacts.social_media.values()):
                        social_found = []
                        for platform, links in contacts.social_media.items():
                            if links:
                                social_found.extend([f"{platform}: {link}" for link in list(links)[:2]])
                        if social_found:
                            print(f"      📱 Social: {social_found[:3]}")
                    if contacts.phone_numbers:
                        print(f"      📞 Phones: {list(contacts.phone_numbers)[:2]}")
                    
                    results.append({
                        "url": url,
                        "success": True,
                        "contacts": contacts.to_dict(),
                        "total_contacts": total_contacts
                    })
                else:
                    print(f"   ❌ Failed: {getattr(result, 'error', 'Unknown error')}")
                    results.append({
                        "url": url,
                        "success": False,
                        "error": getattr(result, 'error', 'Unknown error')
                    })
                
                # Small delay between requests
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"   ❌ Exception: {str(e)}")
                results.append({
                    "url": url,
                    "success": False,
                    "error": str(e)
                })
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 RESULTS SUMMARY")
    print("=" * 40)
    
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}/{len(results)}")
    
    if successful:
        total_contacts = sum(r.get('total_contacts', 0) for r in successful)
        print(f"📧 Total contacts found: {total_contacts}")
        
        # Show sample findings
        print(f"\n🎉 Sample findings:")
        for result in successful[:2]:
            contacts = result['contacts']
            print(f"\n   🌐 {result['url']}")
            if contacts['emails']:
                print(f"      📧 {', '.join(contacts['emails'][:2])}")
            
            social_found = []
            for platform, links in contacts['social_media'].items():
                if links:
                    social_found.extend([f"{platform}: {link}" for link in links[:1]])
            if social_found:
                print(f"      📱 {', '.join(social_found[:2])}")
            
            if contacts['phone_numbers']:
                print(f"      📞 {', '.join(contacts['phone_numbers'][:1])}")
    
    # Save simple results
    output_file = "simple_test_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_summary": {
                "total_urls": len(results),
                "successful": len(successful),
                "failed": len(failed),
                "success_rate": len(successful) / len(results) * 100 if results else 0
            },
            "results": results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 Results saved to: {output_file}")
    
    if successful:
        print("🎉 Test completed successfully!")
        return True
    else:
        print("⚠️  All requests failed. Check your internet connection or try different URLs.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(simple_scrape_test())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)

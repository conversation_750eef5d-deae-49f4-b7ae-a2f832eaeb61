#!/usr/bin/env python3
"""
Working test with fixed Crawl4AI configuration
"""
import asyncio
import pandas as pd
import json
import time
from pathlib import Path

# Import our components
from config import get_config, ScrapingMode
from scraper_engine import ScrapingEngine
from extractors import ContactExtractor


async def test_coffee_shops():
    """Test the scraper with coffee shop data using working configuration"""
    print("🚀 Working Coffee Shop Scraper Test")
    print("=" * 50)
    
    # Read test data
    try:
        df = pd.read_csv('test_data.csv')
        urls = df['website_url'].dropna().tolist()[:5]  # Test first 5 URLs
        print(f"📁 Testing {len(urls)} coffee shop URLs:")
        for i, url in enumerate(urls, 1):
            print(f"   {i}. {url}")
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False
    
    # Configure scraper with working settings
    config = get_config()
    config.scraping.mode = ScrapingMode.HYBRID
    config.scraping.max_concurrent = 3  # Conservative for testing
    config.scraping.request_delay = 2.0  # Respectful delay
    config.scraping.max_retries = 2  # Fewer retries for faster testing
    config.scraping.headless = True
    config.scraping.stealth_mode = True
    
    print(f"\n⚙️  Configuration:")
    print(f"   Mode: {config.scraping.mode}")
    print(f"   Concurrent: {config.scraping.max_concurrent}")
    print(f"   Delay: {config.scraping.request_delay}s")
    print(f"   Retries: {config.scraping.max_retries}")
    
    # Track results
    results = []
    successful_scrapes = 0
    total_contacts = 0
    
    print(f"\n🕷️  Starting scraping...")
    print("-" * 50)
    
    try:
        async with ScrapingEngine(config) as engine:
            
            # Progress callback
            async def progress_callback(completed, total, result):
                nonlocal successful_scrapes, total_contacts
                
                status = "✅" if result.success else "❌"
                contact_info = ""
                
                if result.success and result.contacts:
                    emails = len(result.contacts.emails)
                    social = sum(len(links) for links in result.contacts.social_media.values())
                    phones = len(result.contacts.phone_numbers)
                    contacts_found = emails + social + phones
                    total_contacts += contacts_found
                    successful_scrapes += 1
                    
                    contact_info = f" | 📧{emails} 📱{social} 📞{phones}"
                    
                    # Show some actual findings
                    if emails > 0:
                        sample_emails = list(result.contacts.emails)[:2]
                        contact_info += f" | Emails: {', '.join(sample_emails)}"
                    
                    if social > 0:
                        social_found = []
                        for platform, links in result.contacts.social_media.items():
                            if links:
                                social_found.extend([f"{platform}" for _ in links])
                        if social_found:
                            contact_info += f" | Social: {', '.join(social_found[:2])}"
                
                error_info = f" | Error: {result.error}" if result.error else ""
                
                print(f"{status} [{completed}/{total}] {result.url}{contact_info}{error_info}")
            
            # Run the scraper
            results = await engine.scrape_urls(urls, progress_callback)
            
            # Save results
            json_file = await engine.save_results("working_test_results.json")
            
            # Show final statistics
            stats = engine.get_statistics()
            
            print("\n" + "=" * 50)
            print("📊 FINAL RESULTS")
            print("=" * 50)
            print(f"🎯 Success Rate: {stats['success_rate']:.1f}% ({stats['successful_requests']}/{stats['total_requests']})")
            print(f"⏱️  Total Time: {stats['elapsed_time']:.2f} seconds")
            print(f"🚀 Speed: {stats['requests_per_second']:.2f} requests/second")
            print(f"📧 Emails Found: {stats['total_emails_found']}")
            print(f"📱 Social Links Found: {stats['total_social_links_found']}")
            print(f"📞 Phone Numbers Found: {stats['total_phone_numbers_found']}")
            
            # Show detailed findings
            if successful_scrapes > 0:
                print(f"\n🎉 Detailed Findings:")
                
                for result in results:
                    if result.success and result.contacts:
                        contacts = result.contacts
                        
                        print(f"\n   🌐 {result.url}")
                        
                        if contacts.emails:
                            print(f"      📧 Emails: {', '.join(list(contacts.emails))}")
                        
                        social_found = []
                        for platform, links in contacts.social_media.items():
                            if links:
                                for link in links:
                                    social_found.append(f"{platform}: {link}")
                        
                        if social_found:
                            print(f"      📱 Social Media:")
                            for social in social_found[:3]:  # Show first 3
                                print(f"         - {social}")
                        
                        if contacts.phone_numbers:
                            print(f"      📞 Phone Numbers: {', '.join(list(contacts.phone_numbers))}")
            
            print(f"\n📁 Results saved to: {json_file}")
            
            if successful_scrapes > 0:
                print(f"🎉 Test successful! Found contacts in {successful_scrapes}/{len(urls)} websites")
                return True
            else:
                print("⚠️  No contacts found, but scraper is working")
                return True  # Still consider it successful if scraper worked
                
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(test_coffee_shops())
        if success:
            print("\n✨ Crawl4AI is now working correctly!")
            print("💡 You can now use the full scraper with all your URLs")
        else:
            print("\n❌ Still having issues - check the error messages above")
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        exit(1)

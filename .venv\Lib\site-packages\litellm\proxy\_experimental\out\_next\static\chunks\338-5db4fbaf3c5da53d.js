"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[338],{19250:function(e,t,o){o.r(t),o.d(t,{DEFAULT_ORGANIZATION:function(){return d},PredictedSpendLogsCall:function(){return e9},addAllowedIP:function(){return es},adminGlobalActivity:function(){return eb},adminGlobalActivityExceptions:function(){return eP},adminGlobalActivityExceptionsPerDeployment:function(){return ex},adminGlobalActivityPerModel:function(){return eF},adminGlobalCacheActivity:function(){return eN},adminSpendLogsCall:function(){return eE},adminTopEndUsersCall:function(){return eS},adminTopKeysCall:function(){return ej},adminTopModelsCall:function(){return eO},adminspendByProvider:function(){return ev},alertingSettingsCall:function(){return N},allEndUsersCall:function(){return ek},allTagNamesCall:function(){return em},availableTeamListCall:function(){return M},budgetCreateCall:function(){return j},budgetDeleteCall:function(){return E},budgetUpdateCall:function(){return S},cachingHealthCheckCall:function(){return tw},callMCPTool:function(){return tF},claimOnboardingToken:function(){return X},createGuardrailCall:function(){return tk},createMCPServer:function(){return tS},createPassThroughEndpoint:function(){return ts},credentialCreateCall:function(){return eV},credentialDeleteCall:function(){return eq},credentialGetCall:function(){return eH},credentialListCall:function(){return eD},credentialUpdateCall:function(){return eZ},defaultProxyBaseUrl:function(){return r},deleteAllowedIP:function(){return el},deleteCallback:function(){return t5},deleteConfigFieldSetting:function(){return ti},deleteGuardrailCall:function(){return tK},deleteMCPServer:function(){return tb},deletePassThroughEndpointsCall:function(){return td},fetchMCPAccessGroups:function(){return tj},fetchMCPServers:function(){return tE},getAllowedIPs:function(){return ec},getBudgetList:function(){return te},getBudgetSettings:function(){return tt},getCallbacksCall:function(){return to},getConfigFieldSetting:function(){return tn},getDefaultTeamSettings:function(){return tJ},getEmailEventSettings:function(){return tH},getGeneralSettingsCall:function(){return ta},getGuardrailInfo:function(){return tQ},getGuardrailProviderSpecificParams:function(){return tW},getGuardrailUISettings:function(){return tY},getGuardrailsList:function(){return tm},getInternalUserSettings:function(){return t_},getOnboardingCredentials:function(){return Q},getOpenAPISchema:function(){return m},getPassThroughEndpointInfo:function(){return t4},getPassThroughEndpointsCall:function(){return tr},getPossibleUserRoles:function(){return ez},getProxyBaseUrl:function(){return l},getProxyUISettings:function(){return ty},getPublicModelHubInfo:function(){return y},getRemainingUsers:function(){return t3},getSSOSettings:function(){return t$},getTeamPermissionsCall:function(){return tA},getTotalSpendCall:function(){return W},getUiConfig:function(){return f},healthCheckCall:function(){return tp},healthCheckHistoryCall:function(){return tg},individualModelHealthCheckCall:function(){return tu},invitationClaimCall:function(){return b},invitationCreateCall:function(){return v},keyCreateCall:function(){return P},keyCreateServiceAccountCall:function(){return F},keyDeleteCall:function(){return O},keyInfoCall:function(){return eB},keyInfoV1Call:function(){return eJ},keyListCall:function(){return eU},keySpendLogsCall:function(){return eg},keyUpdateCall:function(){return eK},latestHealthChecksCall:function(){return tf},listMCPTools:function(){return tN},makeModelGroupPublic:function(){return g},mcpToolsCall:function(){return t6},modelAvailableCall:function(){return ew},modelCostMap:function(){return k},modelCreateCall:function(){return C},modelDeleteCall:function(){return T},modelExceptionsCall:function(){return ep},modelHubCall:function(){return en},modelHubPublicModelsCall:function(){return er},modelInfoCall:function(){return eo},modelInfoV1Call:function(){return ea},modelMetricsCall:function(){return ei},modelMetricsSlowResponsesCall:function(){return eh},modelPatchUpdateCall:function(){return eW},modelSettingsCall:function(){return _},modelUpdateCall:function(){return eQ},organizationCreateCall:function(){return V},organizationDeleteCall:function(){return H},organizationInfoCall:function(){return L},organizationListCall:function(){return z},organizationMemberAddCall:function(){return e3},organizationMemberDeleteCall:function(){return e2},organizationMemberUpdateCall:function(){return e4},organizationUpdateCall:function(){return D},proxyBaseUrl:function(){return c},regenerateKeyCall:function(){return $},resetEmailEventSettings:function(){return tZ},serverRootPath:function(){return n},serviceHealthCheck:function(){return e8},sessionSpendLogsCall:function(){return tR},setCallbacksCall:function(){return th},setGlobalLitellmHeaderName:function(){return w},slackBudgetAlertsHealthCheck:function(){return e7},spendUsersCall:function(){return eA},streamingModelMetricsCall:function(){return ed},tagCreateCall:function(){return tP},tagDailyActivityCall:function(){return K},tagDeleteCall:function(){return tG},tagInfoCall:function(){return tO},tagListCall:function(){return tB},tagUpdateCall:function(){return tx},tagsSpendLogsCall:function(){return ey},teamBulkMemberAddCall:function(){return e$},teamCreateCall:function(){return eL},teamDailyActivityCall:function(){return Y},teamDeleteCall:function(){return G},teamInfoCall:function(){return A},teamListCall:function(){return R},teamMemberAddCall:function(){return eX},teamMemberDeleteCall:function(){return e1},teamMemberUpdateCall:function(){return e0},teamPermissionsUpdateCall:function(){return tI},teamSpendLogsCall:function(){return ef},teamUpdateCall:function(){return eY},testConnectionRequest:function(){return eG},testMCPConnectionRequest:function(){return t9},testMCPToolsListRequest:function(){return t7},transformRequestCall:function(){return q},uiAuditLogsCall:function(){return t1},uiSpendLogDetailsCall:function(){return tC},uiSpendLogsCall:function(){return eT},updateConfigFieldSetting:function(){return tl},updateDefaultTeamSettings:function(){return tU},updateEmailEventSettings:function(){return tq},updateGuardrailCall:function(){return tX},updateInternalUserSettings:function(){return tT},updateMCPServer:function(){return tv},updatePassThroughEndpoint:function(){return t2},updatePassThroughFieldSetting:function(){return tc},updateSSOSettings:function(){return t0},updateUsefulLinksCall:function(){return eu},userBulkUpdateUserCall:function(){return e6},userCreateCall:function(){return x},userDailyActivityCall:function(){return Z},userDeleteCall:function(){return B},userFilterUICall:function(){return eC},userGetAllUsersCall:function(){return eM},userGetRequesedtModelsCall:function(){return eR},userInfoCall:function(){return U},userListCall:function(){return J},userRequestModelCall:function(){return eI},userSpendLogsCall:function(){return e_},userUpdateUserCall:function(){return e5},v2TeamListCall:function(){return I},vectorStoreCreateCall:function(){return tM},vectorStoreDeleteCall:function(){return tL},vectorStoreInfoCall:function(){return tV},vectorStoreListCall:function(){return tz},vectorStoreSearchCall:function(){return t8},vectorStoreUpdateCall:function(){return tD}});var a=o(41021);let r=null,n="/",c=null;console.log=function(){};let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=window.location.origin,a=t||o;console.log("proxyBaseUrl:",c),console.log("serverRootPath:",e),e.length>0&&!a.endsWith(e)&&"/"!=e&&(a+=e,c=a),console.log("Updated proxyBaseUrl:",c)},l=()=>c||window.location.origin,i={GET:"GET",DELETE:"DELETE"},d="default_organization",h=0,p=async e=>{let t=Date.now();t-h>6e4?(e.includes("Authentication Error - Expired Key")&&(a.ZP.info("UI Session Expired. Logging out."),h=t,document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",window.location.href=window.location.pathname),h=t):console.log("Error suppressed to prevent spam:",e)},u="Authorization";function w(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Authorization";console.log("setGlobalLitellmHeaderName: ".concat(e)),u=e}let g=async(e,t)=>{let o=c?"".concat(c,"/model_group/make_public"):"/model_group/make_public";return(await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({model_groups:t})})).json()},f=async()=>{console.log("Getting UI config");let e=await fetch(r?"".concat(r,"/litellm/.well-known/litellm-ui-config"):"/litellm/.well-known/litellm-ui-config"),t=await e.json();return console.log("jsonData in getUiConfig:",t),s(t.server_root_path,t.proxy_base_url),t},y=async()=>{let e=await fetch(r?"".concat(r,"/public/model_hub/info"):"/public/model_hub/info");return await e.json()},m=async()=>{let e=c?"".concat(c,"/openapi.json"):"/openapi.json",t=await fetch(e);return await t.json()},k=async e=>{try{let t=c?"".concat(c,"/get/litellm_model_cost_map"):"/get/litellm_model_cost_map",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}}),a=await o.json();return console.log("received litellm model cost data: ".concat(a)),a}catch(e){throw console.error("Failed to get model cost map:",e),e}},C=async(e,t)=>{try{let o=c?"".concat(c,"/model/new"):"/model/new",r=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!r.ok){let e=await r.text()||"Network response was not ok";throw a.ZP.error(e),Error(e)}let n=await r.json();return console.log("API Response:",n),a.ZP.destroy(),a.ZP.success("Model ".concat(t.model_name," created successfully"),2),n}catch(e){throw console.error("Failed to create key:",e),e}},_=async e=>{try{let t=c?"".concat(c,"/model/settings"):"/model/settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){console.error("Failed to get model settings:",e)}},T=async(e,t)=>{console.log("model_id in model delete call: ".concat(t));try{let o=c?"".concat(c,"/model/delete"):"/model/delete",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({id:t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},E=async(e,t)=>{if(console.log("budget_id in budget delete call: ".concat(t)),null!=e)try{let o=c?"".concat(c,"/budget/delete"):"/budget/delete",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({id:t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},j=async(e,t)=>{try{console.log("Form Values in budgetCreateCall:",t),console.log("Form Values after check:",t);let o=c?"".concat(c,"/budget/new"):"/budget/new",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},S=async(e,t)=>{try{console.log("Form Values in budgetUpdateCall:",t),console.log("Form Values after check:",t);let o=c?"".concat(c,"/budget/update"):"/budget/update",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},v=async(e,t)=>{try{let o=c?"".concat(c,"/invitation/new"):"/invitation/new",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},b=async(e,t)=>{try{console.log("Form Values in invitationCreateCall:",t),console.log("Form Values after check:",t);let o=c?"".concat(c,"/invitation/claim"):"/invitation/claim",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},N=async e=>{try{let t=c?"".concat(c,"/alerting/settings"):"/alerting/settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},F=async(e,t)=>{try{if(console.log("Form Values in keyCreateServiceAccountCall:",t),t.description&&(t.metadata||(t.metadata={}),t.metadata.description=t.description,delete t.description,t.metadata=JSON.stringify(t.metadata)),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}console.log("Form Values after check:",t);let o=c?"".concat(c,"/key/service-account/generate"):"/key/service-account/generate",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error(e)}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},P=async(e,t,o)=>{try{if(console.log("Form Values in keyCreateCall:",o),o.description&&(o.metadata||(o.metadata={}),o.metadata.description=o.description,delete o.description,o.metadata=JSON.stringify(o.metadata)),o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}console.log("Form Values after check:",o);let a=c?"".concat(c,"/key/generate"):"/key/generate",r=await fetch(a,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t,...o})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error(e)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},x=async(e,t,o)=>{try{if(console.log("Form Values in keyCreateCall:",o),o.description&&(o.metadata||(o.metadata={}),o.metadata.description=o.description,delete o.description,o.metadata=JSON.stringify(o.metadata)),o.auto_create_key=!1,o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}console.log("Form Values after check:",o);let a=c?"".concat(c,"/user/new"):"/user/new",r=await fetch(a,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_id:t,...o})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error(e)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},O=async(e,t)=>{try{let o=c?"".concat(c,"/key/delete"):"/key/delete";console.log("in keyDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({keys:[t]})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},B=async(e,t)=>{try{let o=c?"".concat(c,"/user/delete"):"/user/delete";console.log("in userDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({user_ids:t})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to delete user(s):",e),e}},G=async(e,t)=>{try{let o=c?"".concat(c,"/team/delete"):"/team/delete";console.log("in teamDeleteCall:",t);let a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_ids:[t]})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to delete key:",e),e}},J=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null,l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,i=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,d=arguments.length>9&&void 0!==arguments[9]?arguments[9]:null;try{let h=c?"".concat(c,"/user/list"):"/user/list";console.log("in userListCall");let w=new URLSearchParams;if(t&&t.length>0){let e=t.join(",");w.append("user_ids",e)}o&&w.append("page",o.toString()),a&&w.append("page_size",a.toString()),r&&w.append("user_email",r),n&&w.append("role",n),s&&w.append("team",s),l&&w.append("sso_user_ids",l),i&&w.append("sort_by",i),d&&w.append("sort_order",d);let g=w.toString();g&&(h+="?".concat(g));let f=await fetch(h,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!f.ok){let e=await f.text();throw p(e),Error("Network response was not ok")}let y=await f.json();return console.log("/user/list API Response:",y),y}catch(e){throw console.error("Failed to create key:",e),e}},U=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,n=arguments.length>5?arguments[5]:void 0,s=arguments.length>6&&void 0!==arguments[6]&&arguments[6];console.log("userInfoCall: ".concat(t,", ").concat(o,", ").concat(a,", ").concat(r,", ").concat(n,", ").concat(s));try{let l;if(a){l=c?"".concat(c,"/user/list"):"/user/list";let e=new URLSearchParams;null!=r&&e.append("page",r.toString()),null!=n&&e.append("page_size",n.toString()),l+="?".concat(e.toString())}else l=c?"".concat(c,"/user/info"):"/user/info",("Admin"!==o&&"Admin Viewer"!==o||s)&&t&&(l+="?user_id=".concat(t));console.log("Requesting user data from:",l);let i=await fetch(l,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.text();throw p(e),Error("Network response was not ok")}let d=await i.json();return console.log("API Response:",d),d}catch(e){throw console.error("Failed to fetch user data:",e),e}},A=async(e,t)=>{try{let o=c?"".concat(c,"/team/info"):"/team/info";t&&(o="".concat(o,"?team_id=").concat(t)),console.log("in teamInfoCall");let a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},I=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;arguments.length>5&&void 0!==arguments[5]&&arguments[5],arguments.length>6&&void 0!==arguments[6]&&arguments[6],arguments.length>7&&void 0!==arguments[7]&&arguments[7],arguments.length>8&&void 0!==arguments[8]&&arguments[8];try{let n=c?"".concat(c,"/v2/team/list"):"/v2/team/list";console.log("in teamInfoCall");let s=new URLSearchParams;o&&s.append("user_id",o.toString()),t&&s.append("organization_id",t.toString()),a&&s.append("team_id",a.toString()),r&&s.append("team_alias",r.toString());let l=s.toString();l&&(n+="?".concat(l));let i=await fetch(n,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.text();throw p(e),Error("Network response was not ok")}let d=await i.json();return console.log("/v2/team/list API Response:",d),d}catch(e){throw console.error("Failed to create key:",e),e}},R=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let n=c?"".concat(c,"/team/list"):"/team/list";console.log("in teamInfoCall");let s=new URLSearchParams;o&&s.append("user_id",o.toString()),t&&s.append("organization_id",t.toString()),a&&s.append("team_id",a.toString()),r&&s.append("team_alias",r.toString());let l=s.toString();l&&(n+="?".concat(l));let i=await fetch(n,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.text();throw p(e),Error("Network response was not ok")}let d=await i.json();return console.log("/team/list API Response:",d),d}catch(e){throw console.error("Failed to create key:",e),e}},M=async e=>{try{let t=c?"".concat(c,"/team/available"):"/team/available";console.log("in availableTeamListCall");let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("/team/available_teams API Response:",a),a}catch(e){throw e}},z=async e=>{try{let t=c?"".concat(c,"/organization/list"):"/organization/list",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},L=async(e,t)=>{try{let o=c?"".concat(c,"/organization/info"):"/organization/info";t&&(o="".concat(o,"?organization_id=").concat(t)),console.log("in teamInfoCall");let a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},V=async(e,t)=>{try{if(console.log("Form Values in organizationCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw console.error("Failed to parse metadata:",e),Error("Failed to parse metadata: "+e)}}let o=c?"".concat(c,"/organization/new"):"/organization/new",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},D=async(e,t)=>{try{console.log("Form Values in organizationUpdateCall:",t);let o=c?"".concat(c,"/organization/update"):"/organization/update",a=await fetch(o,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("Update Team Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},H=async(e,t)=>{try{let o=c?"".concat(c,"/organization/delete"):"/organization/delete",a=await fetch(o,{method:"DELETE",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_ids:[t]})});if(!a.ok){let e=await a.text();throw p(e),Error("Error deleting organization: ".concat(e))}return await a.json()}catch(e){throw console.error("Failed to delete organization:",e),e}},q=async(e,t)=>{try{let o=c?"".concat(c,"/utils/transform_request"):"/utils/transform_request",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},Z=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;try{let r=c?"".concat(c,"/user/daily/activity"):"/user/daily/activity",n=new URLSearchParams,s=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};n.append("start_date",s(t)),n.append("end_date",s(o)),n.append("page_size","1000"),n.append("page",a.toString());let l=n.toString();l&&(r+="?".concat(l));let i=await fetch(r,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!i.ok){let e=await i.text();throw p(e),Error("Network response was not ok")}return await i.json()}catch(e){throw console.error("Failed to create key:",e),e}},K=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let n=c?"".concat(c,"/tag/daily/activity"):"/tag/daily/activity",s=new URLSearchParams,l=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};s.append("start_date",l(t)),s.append("end_date",l(o)),s.append("page_size","1000"),s.append("page",a.toString()),r&&s.append("tags",r.join(","));let i=s.toString();i&&(n+="?".concat(i));let d=await fetch(n,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!d.ok){let e=await d.text();throw p(e),Error("Network response was not ok")}return await d.json()}catch(e){throw console.error("Failed to create key:",e),e}},Y=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{let n=c?"".concat(c,"/team/daily/activity"):"/team/daily/activity",s=new URLSearchParams,l=e=>{let t=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(o,"-").concat(a)};s.append("start_date",l(t)),s.append("end_date",l(o)),s.append("page_size","1000"),s.append("page",a.toString()),r&&s.append("team_ids",r.join(",")),s.append("exclude_team_ids","litellm-dashboard");let i=s.toString();i&&(n+="?".concat(i));let d=await fetch(n,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!d.ok){let e=await d.text();throw p(e),Error("Network response was not ok")}return await d.json()}catch(e){throw console.error("Failed to create key:",e),e}},W=async e=>{try{let t=c?"".concat(c,"/global/spend"):"/global/spend",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},Q=async e=>{try{let t=c?"".concat(c,"/onboarding/get_token"):"/onboarding/get_token";t+="?invite_link=".concat(e);let o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},X=async(e,t,o,a)=>{let r=c?"".concat(c,"/onboarding/claim_token"):"/onboarding/claim_token";try{let n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({invitation_link:t,user_id:o,password:a})});if(!n.ok){let e=await n.text();throw p(e),Error("Network response was not ok")}let c=await n.json();return console.log(c),c}catch(e){throw console.error("Failed to delete key:",e),e}},$=async(e,t,o)=>{try{let a=c?"".concat(c,"/key/").concat(t,"/regenerate"):"/key/".concat(t,"/regenerate"),r=await fetch(a,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!r.ok){let e=await r.text();throw p(e),Error("Network response was not ok")}let n=await r.json();return console.log("Regenerate key Response:",n),n}catch(e){throw console.error("Failed to regenerate key:",e),e}},ee=!1,et=null,eo=async(e,t,o)=>{try{console.log("modelInfoCall:",e,t,o);let r=c?"".concat(c,"/v2/model/info"):"/v2/model/info",n=new URLSearchParams;n.append("include_team_models","true"),n.toString()&&(r+="?".concat(n.toString()));let s=await fetch(r,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw e+="error shown=".concat(ee),ee||(e.includes("No model list passed")&&(e="No Models Exist. Click Add Model to get started."),a.ZP.info(e,10),ee=!0,et&&clearTimeout(et),et=setTimeout(()=>{ee=!1},1e4)),Error("Network response was not ok")}let l=await s.json();return console.log("modelInfoCall:",l),l}catch(e){throw console.error("Failed to create key:",e),e}},ea=async(e,t)=>{try{let o=c?"".concat(c,"/v1/model/info"):"/v1/model/info";o+="?litellm_model_id=".concat(t);let a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok)throw await a.text(),Error("Network response was not ok");let r=await a.json();return console.log("modelInfoV1Call:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},er=async()=>{let e=c?"".concat(c,"/public/model_hub"):"/public/model_hub";return(await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}})).json()},en=async e=>{try{let t=c?"".concat(c,"/model_group/info"):"/model_group/info",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log("modelHubCall:",a),a}catch(e){throw console.error("Failed to create key:",e),e}},ec=async e=>{try{let t=c?"".concat(c,"/get/allowed_ips"):"/get/allowed_ips",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw Error("Network response was not ok: ".concat(e))}let a=await o.json();return console.log("getAllowedIPs:",a),a.data}catch(e){throw console.error("Failed to get allowed IPs:",e),e}},es=async(e,t)=>{try{let o=c?"".concat(c,"/add/allowed_ip"):"/add/allowed_ip",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({ip:t})});if(!a.ok){let e=await a.text();throw Error("Network response was not ok: ".concat(e))}let r=await a.json();return console.log("addAllowedIP:",r),r}catch(e){throw console.error("Failed to add allowed IP:",e),e}},el=async(e,t)=>{try{let o=c?"".concat(c,"/delete/allowed_ip"):"/delete/allowed_ip",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({ip:t})});if(!a.ok){let e=await a.text();throw Error("Network response was not ok: ".concat(e))}let r=await a.json();return console.log("deleteAllowedIP:",r),r}catch(e){throw console.error("Failed to delete allowed IP:",e),e}},ei=async(e,t,o,a,r,n,s,l)=>{try{let t=c?"".concat(c,"/model/metrics"):"/model/metrics";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(r,"&endTime=").concat(n,"&api_key=").concat(s,"&customer=").concat(l));let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},ed=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/model/streaming_metrics"):"/model/streaming_metrics";t&&(r="".concat(r,"?_selected_model_group=").concat(t,"&startTime=").concat(o,"&endTime=").concat(a));let n=await fetch(r,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok){let e=await n.text();throw p(e),Error("Network response was not ok")}return await n.json()}catch(e){throw console.error("Failed to create key:",e),e}},eh=async(e,t,o,a,r,n,s,l)=>{try{let t=c?"".concat(c,"/model/metrics/slow_responses"):"/model/metrics/slow_responses";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(r,"&endTime=").concat(n,"&api_key=").concat(s,"&customer=").concat(l));let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},ep=async(e,t,o,a,r,n,s,l)=>{try{let t=c?"".concat(c,"/model/metrics/exceptions"):"/model/metrics/exceptions";a&&(t="".concat(t,"?_selected_model_group=").concat(a,"&startTime=").concat(r,"&endTime=").concat(n,"&api_key=").concat(s,"&customer=").concat(l));let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to create key:",e),e}},eu=async(e,t)=>{try{let o=c?"".concat(c,"/model_hub/update_useful_links"):"/model_hub/update_useful_links",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({useful_links:t})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},ew=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=(arguments.length>5&&void 0!==arguments[5]&&arguments[5],arguments.length>6&&void 0!==arguments[6]&&arguments[6]);console.log("in /models calls, globalLitellmHeaderName",u);try{let t=c?"".concat(c,"/models"):"/models",o=new URLSearchParams;o.append("include_model_access_groups","True"),!0===a&&o.append("return_wildcard_routes","True"),!0===n&&o.append("only_model_access_groups","True"),r&&o.append("team_id",r.toString()),o.toString()&&(t+="?".concat(o.toString()));let s=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw p(e),Error("Network response was not ok")}return await s.json()}catch(e){throw console.error("Failed to create key:",e),e}},eg=async(e,t)=>{try{let o=c?"".concat(c,"/global/spend/logs"):"/global/spend/logs";console.log("in keySpendLogsCall:",o);let a=await fetch("".concat(o,"?api_key=").concat(t),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},ef=async e=>{try{let t=c?"".concat(c,"/global/spend/teams"):"/global/spend/teams";console.log("in teamSpendLogsCall:",t);let o=await fetch("".concat(t),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ey=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/global/spend/tags"):"/global/spend/tags";t&&o&&(r="".concat(r,"?start_date=").concat(t,"&end_date=").concat(o)),a&&(r+="".concat(r,"&tags=").concat(a.join(","))),console.log("in tagsSpendLogsCall:",r);let n=await fetch("".concat(r),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!n.ok)throw await n.text(),Error("Network response was not ok");let s=await n.json();return console.log(s),s}catch(e){throw console.error("Failed to create key:",e),e}},em=async e=>{try{let t=c?"".concat(c,"/global/spend/all_tag_names"):"/global/spend/all_tag_names";console.log("in global/spend/all_tag_names call",t);let o=await fetch("".concat(t),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ek=async e=>{try{let t=c?"".concat(c,"/global/all_end_users"):"/global/all_end_users";console.log("in global/all_end_users call",t);let o=await fetch("".concat(t),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eC=async(e,t)=>{try{let o=c?"".concat(c,"/user/filter/ui"):"/user/filter/ui";t.get("user_email")&&(o+="?user_email=".concat(t.get("user_email"))),t.get("user_id")&&(o+="?user_id=".concat(t.get("user_id")));let a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to create key:",e),e}},e_=async(e,t,o,a,r,n)=>{try{console.log("user role in spend logs call: ".concat(o));let t=c?"".concat(c,"/spend/logs"):"/spend/logs";t="App Owner"==o?"".concat(t,"?user_id=").concat(a,"&start_date=").concat(r,"&end_date=").concat(n):"".concat(t,"?start_date=").concat(r,"&end_date=").concat(n);let s=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw p(e),Error("Network response was not ok")}let l=await s.json();return console.log(l),l}catch(e){throw console.error("Failed to create key:",e),e}},eT=async(e,t,o,a,r,n,s,l,i,d,h,w)=>{try{let g=c?"".concat(c,"/spend/logs/ui"):"/spend/logs/ui",f=new URLSearchParams;t&&f.append("api_key",t),o&&f.append("team_id",o),a&&f.append("request_id",a),r&&f.append("start_date",r),n&&f.append("end_date",n),s&&f.append("page",s.toString()),l&&f.append("page_size",l.toString()),i&&f.append("user_id",i),d&&f.append("end_user",d),h&&f.append("status_filter",h),w&&f.append("model",w);let y=f.toString();y&&(g+="?".concat(y));let m=await fetch(g,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!m.ok){let e=await m.text();throw p(e),Error("Network response was not ok")}let k=await m.json();return console.log("Spend Logs Response:",k),k}catch(e){throw console.error("Failed to fetch spend logs:",e),e}},eE=async e=>{try{let t=c?"".concat(c,"/global/spend/logs"):"/global/spend/logs",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},ej=async e=>{try{let t=c?"".concat(c,"/global/spend/keys?limit=5"):"/global/spend/keys?limit=5",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eS=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/global/spend/end_users"):"/global/spend/end_users",n="";n=t?JSON.stringify({api_key:t,startTime:o,endTime:a}):JSON.stringify({startTime:o,endTime:a});let s={method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:n},l=await fetch(r,s);if(!l.ok){let e=await l.text();throw p(e),Error("Network response was not ok")}let i=await l.json();return console.log(i),i}catch(e){throw console.error("Failed to create key:",e),e}},ev=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/global/spend/provider"):"/global/spend/provider";o&&a&&(r+="?start_date=".concat(o,"&end_date=").concat(a)),t&&(r+="&api_key=".concat(t));let n={method:"GET",headers:{[u]:"Bearer ".concat(e)}},s=await fetch(r,n);if(!s.ok){let e=await s.text();throw p(e),Error("Network response was not ok")}let l=await s.json();return console.log(l),l}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eb=async(e,t,o)=>{try{let a=c?"".concat(c,"/global/activity"):"/global/activity";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let r={method:"GET",headers:{[u]:"Bearer ".concat(e)}},n=await fetch(a,r);if(!n.ok)throw await n.text(),Error("Network response was not ok");let s=await n.json();return console.log(s),s}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eN=async(e,t,o)=>{try{let a=c?"".concat(c,"/global/activity/cache_hits"):"/global/activity/cache_hits";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let r={method:"GET",headers:{[u]:"Bearer ".concat(e)}},n=await fetch(a,r);if(!n.ok)throw await n.text(),Error("Network response was not ok");let s=await n.json();return console.log(s),s}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eF=async(e,t,o)=>{try{let a=c?"".concat(c,"/global/activity/model"):"/global/activity/model";t&&o&&(a+="?start_date=".concat(t,"&end_date=").concat(o));let r={method:"GET",headers:{[u]:"Bearer ".concat(e)}},n=await fetch(a,r);if(!n.ok)throw await n.text(),Error("Network response was not ok");let s=await n.json();return console.log(s),s}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eP=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/global/activity/exceptions"):"/global/activity/exceptions";t&&o&&(r+="?start_date=".concat(t,"&end_date=").concat(o)),a&&(r+="&model_group=".concat(a));let n={method:"GET",headers:{[u]:"Bearer ".concat(e)}},s=await fetch(r,n);if(!s.ok)throw await s.text(),Error("Network response was not ok");let l=await s.json();return console.log(l),l}catch(e){throw console.error("Failed to fetch spend data:",e),e}},ex=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/global/activity/exceptions/deployment"):"/global/activity/exceptions/deployment";t&&o&&(r+="?start_date=".concat(t,"&end_date=").concat(o)),a&&(r+="&model_group=".concat(a));let n={method:"GET",headers:{[u]:"Bearer ".concat(e)}},s=await fetch(r,n);if(!s.ok)throw await s.text(),Error("Network response was not ok");let l=await s.json();return console.log(l),l}catch(e){throw console.error("Failed to fetch spend data:",e),e}},eO=async e=>{try{let t=c?"".concat(c,"/global/spend/models?limit=5"):"/global/spend/models?limit=5",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to create key:",e),e}},eB=async(e,t)=>{try{let o=c?"".concat(c,"/v2/key/info"):"/v2/key/info",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({keys:t})});if(!a.ok){let e=await a.text();if(e.includes("Invalid proxy server token passed"))throw Error("Invalid proxy server token passed");throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},eG=async(e,t,o)=>{try{console.log("Sending model connection test request:",JSON.stringify(t));let r=c?"".concat(c,"/health/test_connection"):"/health/test_connection",n=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json",[u]:"Bearer ".concat(e)},body:JSON.stringify({litellm_params:t,mode:o})}),s=n.headers.get("content-type");if(!s||!s.includes("application/json")){let e=await n.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(n.status,": ").concat(n.statusText,"). Check network tab for details."))}let l=await n.json();if(!n.ok||"error"===l.status){if("error"===l.status);else{var a;return{status:"error",message:(null===(a=l.error)||void 0===a?void 0:a.message)||"Connection test failed: ".concat(n.status," ").concat(n.statusText)}}}return l}catch(e){throw console.error("Model connection test error:",e),e}},eJ=async(e,t)=>{try{console.log("entering keyInfoV1Call");let o=c?"".concat(c,"/key/info"):"/key/info";o="".concat(o,"?key=").concat(t);let r=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(console.log("response",r),!r.ok){let e=await r.text();p(e),a.ZP.error("Failed to fetch key info - "+e)}let n=await r.json();return console.log("data",n),n}catch(e){throw console.error("Failed to fetch key info:",e),e}},eU=async function(e,t,o,a,r,n,s,l){let i=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,d=arguments.length>9&&void 0!==arguments[9]?arguments[9]:null;try{let h=c?"".concat(c,"/key/list"):"/key/list";console.log("in keyListCall");let w=new URLSearchParams;o&&w.append("team_id",o.toString()),t&&w.append("organization_id",t.toString()),a&&w.append("key_alias",a),n&&w.append("key_hash",n),r&&w.append("user_id",r.toString()),s&&w.append("page",s.toString()),l&&w.append("size",l.toString()),i&&w.append("sort_by",i),d&&w.append("sort_order",d),w.append("return_full_object","true"),w.append("include_team_keys","true");let g=w.toString();g&&(h+="?".concat(g));let f=await fetch(h,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!f.ok){let e=await f.text();throw p(e),Error("Network response was not ok")}let y=await f.json();return console.log("/team/list API Response:",y),y}catch(e){throw console.error("Failed to create key:",e),e}},eA=async(e,t)=>{try{let o=c?"".concat(c,"/spend/users"):"/spend/users";console.log("in spendUsersCall:",o);let a=await fetch("".concat(o,"?user_id=").concat(t),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to get spend for user",e),e}},eI=async(e,t,o,a)=>{try{let r=c?"".concat(c,"/user/request_model"):"/user/request_model",n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({models:[t],user_id:o,justification:a})});if(!n.ok){let e=await n.text();throw p(e),Error("Network response was not ok")}let s=await n.json();return console.log(s),s}catch(e){throw console.error("Failed to create key:",e),e}},eR=async e=>{try{let t=c?"".concat(c,"/user/get_requests"):"/user/get_requests";console.log("in userGetRequesedtModelsCall:",t);let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log(a),a}catch(e){throw console.error("Failed to get requested models:",e),e}},eM=async(e,t)=>{try{let o=c?"".concat(c,"/user/get_users?role=").concat(t):"/user/get_users?role=".concat(t);console.log("in userGetAllUsersCall:",o);let a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to get requested models:",e),e}},ez=async e=>{try{let t=c?"".concat(c,"/user/available_roles"):"/user/available_roles",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");let a=await o.json();return console.log("response from user/available_role",a),a}catch(e){throw e}},eL=async(e,t)=>{try{if(console.log("Form Values in teamCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let o=c?"".concat(c,"/team/new"):"/team/new",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},eV=async(e,t)=>{try{if(console.log("Form Values in credentialCreateCall:",t),t.metadata){console.log("formValues.metadata:",t.metadata);try{t.metadata=JSON.parse(t.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let o=c?"".concat(c,"/credentials"):"/credentials",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},eD=async e=>{try{let t=c?"".concat(c,"/credentials"):"/credentials";console.log("in credentialListCall");let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("/credentials API Response:",a),a}catch(e){throw console.error("Failed to create key:",e),e}},eH=async(e,t,o)=>{try{let a=c?"".concat(c,"/credentials"):"/credentials";t?a+="/by_name/".concat(t):o&&(a+="/by_model/".concat(o)),console.log("in credentialListCall");let r=await fetch(a,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok){let e=await r.text();throw p(e),Error("Network response was not ok")}let n=await r.json();return console.log("/credentials API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},eq=async(e,t)=>{try{let o=c?"".concat(c,"/credentials/").concat(t):"/credentials/".concat(t);console.log("in credentialDeleteCall:",t);let a=await fetch(o,{method:"DELETE",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to delete key:",e),e}},eZ=async(e,t,o)=>{try{if(console.log("Form Values in credentialUpdateCall:",o),o.metadata){console.log("formValues.metadata:",o.metadata);try{o.metadata=JSON.parse(o.metadata)}catch(e){throw Error("Failed to parse metadata: "+e)}}let a=c?"".concat(c,"/credentials/").concat(t):"/credentials/".concat(t),r=await fetch(a,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...o})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},eK=async(e,t)=>{try{if(console.log("Form Values in keyUpdateCall:",t),t.model_tpm_limit){console.log("formValues.model_tpm_limit:",t.model_tpm_limit);try{t.model_tpm_limit=JSON.parse(t.model_tpm_limit)}catch(e){throw Error("Failed to parse model_tpm_limit: "+e)}}if(t.model_rpm_limit){console.log("formValues.model_rpm_limit:",t.model_rpm_limit);try{t.model_rpm_limit=JSON.parse(t.model_rpm_limit)}catch(e){throw Error("Failed to parse model_rpm_limit: "+e)}}let o=c?"".concat(c,"/key/update"):"/key/update",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("Update key Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},eY=async(e,t)=>{try{console.log("Form Values in teamUpateCall:",t);let o=c?"".concat(c,"/team/update"):"/team/update",r=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),a.ZP.error("Failed to update team settings: "+e),Error(e)}let n=await r.json();return console.log("Update Team Response:",n),n}catch(e){throw console.error("Failed to update team:",e),e}},eW=async(e,t,o)=>{try{console.log("Form Values in modelUpateCall:",t);let a=c?"".concat(c,"/model/").concat(o,"/update"):"/model/".concat(o,"/update"),r=await fetch(a,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error update from the server:",e),Error("Network response was not ok")}let n=await r.json();return console.log("Update model Response:",n),n}catch(e){throw console.error("Failed to update model:",e),e}},eQ=async(e,t)=>{try{console.log("Form Values in modelUpateCall:",t);let o=c?"".concat(c,"/model/update"):"/model/update",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error update from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("Update model Response:",r),r}catch(e){throw console.error("Failed to update model:",e),e}},eX=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let r=c?"".concat(c,"/team/member_add"):"/team/member_add",n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,member:o})});if(!n.ok){var a;let e=await n.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(a=t.detail)||void 0===a?void 0:a.error)||"Failed to add team member",r=Error(o);throw r.raw=t,r}let s=await n.json();return console.log("API Response:",s),s}catch(e){throw console.error("Failed to create key:",e),e}},e$=async(e,t,o,a,r)=>{try{console.log("Bulk add team members:",{teamId:t,members:o,maxBudgetInTeam:a});let s=c?"".concat(c,"/team/bulk_member_add"):"/team/bulk_member_add",l={team_id:t};r?l.all_users=!0:l.members=o,null!=a&&(l.max_budget_in_team=a);let i=await fetch(s,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(l)});if(!i.ok){var n;let e=await i.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(n=t.detail)||void 0===n?void 0:n.error)||"Failed to bulk add team members",a=Error(o);throw a.raw=t,a}let d=await i.json();return console.log("Bulk team member add API Response:",d),d}catch(e){throw console.error("Failed to bulk add team members:",e),e}},e0=async(e,t,o)=>{try{console.log("Form Values in teamMemberUpdateCall:",o);let r=c?"".concat(c,"/team/member_update"):"/team/member_update",n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,role:o.role,user_id:o.user_id})});if(!n.ok){var a;let e=await n.text(),t={};try{t=JSON.parse(e)}catch(t){console.warn("Failed to parse error body as JSON:",e)}let o=(null==t?void 0:null===(a=t.detail)||void 0===a?void 0:a.error)||"Failed to add team member",r=Error(o);throw r.raw=t,r}let s=await n.json();return console.log("API Response:",s),s}catch(e){throw console.error("Failed to update team member:",e),e}},e1=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let a=c?"".concat(c,"/team/member_delete"):"/team/member_delete",r=await fetch(a,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({team_id:t,...void 0!==o.user_email&&{user_email:o.user_email},...void 0!==o.user_id&&{user_id:o.user_id}})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create key:",e),e}},e3=async(e,t,o)=>{try{console.log("Form Values in teamMemberAddCall:",o);let a=c?"".concat(c,"/organization/member_add"):"/organization/member_add",r=await fetch(a,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,member:o})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error(e)}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to create organization member:",e),e}},e2=async(e,t,o)=>{try{console.log("Form Values in organizationMemberDeleteCall:",o);let a=c?"".concat(c,"/organization/member_delete"):"/organization/member_delete",r=await fetch(a,{method:"DELETE",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,user_id:o})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to delete organization member:",e),e}},e4=async(e,t,o)=>{try{console.log("Form Values in organizationMemberUpdateCall:",o);let a=c?"".concat(c,"/organization/member_update"):"/organization/member_update",r=await fetch(a,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({organization_id:t,...o})});if(!r.ok){let e=await r.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let n=await r.json();return console.log("API Response:",n),n}catch(e){throw console.error("Failed to update organization member:",e),e}},e5=async(e,t,o)=>{try{console.log("Form Values in userUpdateUserCall:",t);let a=c?"".concat(c,"/user/update"):"/user/update",r={...t};null!==o&&(r.user_role=o),r=JSON.stringify(r);let n=await fetch(a,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:r});if(!n.ok){let e=await n.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let s=await n.json();return console.log("API Response:",s),s}catch(e){throw console.error("Failed to create key:",e),e}},e6=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];try{let r;console.log("Form Values in userUpdateUserCall:",t);let n=c?"".concat(c,"/user/bulk_update"):"/user/bulk_update";if(a)r=JSON.stringify({all_users:!0,user_updates:t});else if(o&&o.length>0){let e=[];for(let a of o)e.push({user_id:a,...t});r=JSON.stringify({users:e})}else throw Error("Must provide either userIds or set allUsers=true");let s=await fetch(n,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:r});if(!s.ok){let e=await s.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let l=await s.json();return console.log("API Response:",l),l}catch(e){throw console.error("Failed to create key:",e),e}},e9=async(e,t)=>{try{let o=c?"".concat(c,"/global/predict/spend/logs"):"/global/predict/spend/logs",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({data:t})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log(r),r}catch(e){throw console.error("Failed to create key:",e),e}},e7=async e=>{try{let t=c?"".concat(c,"/health/services?service=slack_budget_alerts"):"/health/services?service=slack_budget_alerts";console.log("Checking Slack Budget Alerts service health");let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error(e)}let r=await o.json();return a.ZP.success("Test Slack Alert worked - check your Slack!"),console.log("Service Health Response:",r),r}catch(e){throw console.error("Failed to perform health check:",e),e}},e8=async(e,t)=>{try{let o=c?"".concat(c,"/health/services?service=").concat(t):"/health/services?service=".concat(t);console.log("Checking Slack Budget Alerts service health");let r=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok){let e=await r.text();throw p(e),Error(e)}let n=await r.json();return a.ZP.success("Test request to ".concat(t," made - check logs/alerts on ").concat(t," to verify")),n}catch(e){throw console.error("Failed to perform health check:",e),e}},te=async e=>{try{let t=c?"".concat(c,"/budget/list"):"/budget/list",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tt=async e=>{try{let t=c?"".concat(c,"/budget/settings"):"/budget/settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},to=async(e,t,o)=>{try{let t=c?"".concat(c,"/get/config/callbacks"):"/get/config/callbacks",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},ta=async e=>{try{let t=c?"".concat(c,"/config/list?config_type=general_settings"):"/config/list?config_type=general_settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tr=async e=>{try{let t=c?"".concat(c,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tn=async(e,t)=>{try{let o=c?"".concat(c,"/config/field/info?field_name=").concat(t):"/config/field/info?field_name=".concat(t),a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok)throw await a.text(),Error("Network response was not ok");return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},tc=async(e,t,o)=>{try{let r=c?"".concat(c,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,field_value:o})});if(!n.ok){let e=await n.text();throw p(e),Error("Network response was not ok")}let s=await n.json();return a.ZP.success("Successfully updated value!"),s}catch(e){throw console.error("Failed to set callbacks:",e),e}},ts=async(e,t)=>{try{let o=c?"".concat(c,"/config/pass_through_endpoint"):"/config/pass_through_endpoint",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},tl=async(e,t,o)=>{try{let r=c?"".concat(c,"/config/field/update"):"/config/field/update",n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,field_value:o,config_type:"general_settings"})});if(!n.ok){let e=await n.text();throw p(e),Error("Network response was not ok")}let s=await n.json();return a.ZP.success("Successfully updated value!"),s}catch(e){throw console.error("Failed to set callbacks:",e),e}},ti=async(e,t)=>{try{let o=c?"".concat(c,"/config/field/delete"):"/config/field/delete",r=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({field_name:t,config_type:"general_settings"})});if(!r.ok){let e=await r.text();throw p(e),Error("Network response was not ok")}let n=await r.json();return a.ZP.success("Field reset on proxy"),n}catch(e){throw console.error("Failed to get callbacks:",e),e}},td=async(e,t)=>{try{let o=c?"".concat(c,"/config/pass_through_endpoint?endpoint_id=").concat(t):"/config/pass_through_endpoint".concat(t),a=await fetch(o,{method:"DELETE",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},th=async(e,t)=>{try{let o=c?"".concat(c,"/config/update"):"/config/update",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to set callbacks:",e),e}},tp=async e=>{try{let t=c?"".concat(c,"/health"):"/health",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to call /health:",e),e}},tu=async(e,t)=>{try{let o=c?"".concat(c,"/health?model=").concat(encodeURIComponent(t)):"/health?model=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw Error(e||"Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to call /health for model ".concat(t,":"),e),e}},tw=async e=>{try{let t=c?"".concat(c,"/cache/ping"):"/cache/ping",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error(e)}return await o.json()}catch(e){throw console.error("Failed to call /cache/ping:",e),e}},tg=async function(e,t,o){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:100,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;try{let n=c?"".concat(c,"/health/history"):"/health/history",s=new URLSearchParams;t&&s.append("model",t),o&&s.append("status_filter",o),s.append("limit",a.toString()),s.append("offset",r.toString()),s.toString()&&(n+="?".concat(s.toString()));let l=await fetch(n,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!l.ok){let e=await l.text();throw p(e),Error(e)}return await l.json()}catch(e){throw console.error("Failed to call /health/history:",e),e}},tf=async e=>{try{let t=c?"".concat(c,"/health/latest"):"/health/latest",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error(e)}return await o.json()}catch(e){throw console.error("Failed to call /health/latest:",e),e}},ty=async e=>{try{console.log("Getting proxy UI settings"),console.log("proxyBaseUrl in getProxyUISettings:",c);let t=c?"".concat(c,"/sso/get/ui_settings"):"/sso/get/ui_settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw await o.text(),Error("Network response was not ok");return await o.json()}catch(e){throw console.error("Failed to get callbacks:",e),e}},tm=async e=>{try{let t=c?"".concat(c,"/v2/guardrails/list"):"/v2/guardrails/list",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to get guardrails list:",e),e}},tk=async(e,t)=>{try{let o=c?"".concat(c,"/guardrails"):"/guardrails",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({guardrail:t})});if(!a.ok){let e=await a.text();throw p(e),Error(e)}let r=await a.json();return console.log("Create guardrail response:",r),r}catch(e){throw console.error("Failed to create guardrail:",e),e}},tC=async(e,t,o)=>{try{let a=c?"".concat(c,"/spend/logs/ui/").concat(t,"?start_date=").concat(encodeURIComponent(o)):"/spend/logs/ui/".concat(t,"?start_date=").concat(encodeURIComponent(o));console.log("Fetching log details from:",a);let r=await fetch(a,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!r.ok){let e=await r.text();throw p(e),Error("Network response was not ok")}let n=await r.json();return console.log("Fetched log details:",n),n}catch(e){throw console.error("Failed to fetch log details:",e),e}},t_=async e=>{try{let t=c?"".concat(c,"/get/internal_user_settings"):"/get/internal_user_settings";console.log("Fetching SSO settings from:",t);let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched SSO settings:",a),a}catch(e){throw console.error("Failed to fetch SSO settings:",e),e}},tT=async(e,t)=>{try{let o=c?"".concat(c,"/update/internal_user_settings"):"/update/internal_user_settings";console.log("Updating internal user settings:",t);let r=await fetch(o,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.text();throw p(e),Error(e)}let n=await r.json();return console.log("Updated internal user settings:",n),a.ZP.success("Internal user settings updated successfully"),n}catch(e){throw console.error("Failed to update internal user settings:",e),e}},tE=async e=>{try{let t=c?"".concat(c,"/v1/mcp/server"):"/v1/mcp/server";console.log("Fetching MCP servers from:",t);let o=await fetch(t,{method:i.GET,headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched MCP servers:",a),a}catch(e){throw console.error("Failed to fetch MCP servers:",e),e}},tj=async e=>{try{let t=c?"".concat(c,"/v1/mcp/access_groups"):"/v1/mcp/access_groups";console.log("Fetching MCP access groups from:",t);let o=await fetch(t,{method:i.GET,headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched MCP access groups:",a),a.access_groups||[]}catch(e){throw console.error("Failed to fetch MCP access groups:",e),e}},tS=async(e,t)=>{try{console.log("Form Values in createMCPServer:",t);let o=c?"".concat(c,"/v1/mcp/server"):"/v1/mcp/server",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({...t})});if(!a.ok){let e=await a.text();throw p(e),console.error("Error response from the server:",e),Error("Network response was not ok")}let r=await a.json();return console.log("API Response:",r),r}catch(e){throw console.error("Failed to create key:",e),e}},tv=async(e,t)=>{try{let o=c?"".concat(c,"/v1/mcp/server"):"/v1/mcp/server",a=await fetch(o,{method:"PUT",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to update MCP server:",e),e}},tb=async(e,t)=>{try{let o=(c?"".concat(c):"")+"/v1/mcp/server/".concat(t);console.log("in deleteMCPServer:",t);let a=await fetch(o,{method:i.DELETE,headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}}catch(e){throw console.error("Failed to delete key:",e),e}},tN=async(e,t)=>{try{let o=c?"".concat(c,"/mcp-rest/tools/list?server_id=").concat(t):"/mcp-rest/tools/list?server_id=".concat(t);console.log("Fetching MCP tools from:",o);let a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}}),r=await a.json();if(console.log("Fetched MCP tools response:",r),!a.ok){if(r.error&&r.message)throw Error(r.message);throw Error("Failed to fetch MCP tools")}return r}catch(e){return console.error("Failed to fetch MCP tools:",e),{tools:[],error:"network_error",message:e instanceof Error?e.message:"Failed to fetch MCP tools"}}},tF=async(e,t,o,a,r)=>{try{let n=c?"".concat(c,"/mcp-rest/tools/call"):"/mcp-rest/tools/call";console.log("Calling MCP tool:",t,"with arguments:",o);let s={[u]:"Bearer ".concat(e),"Content-Type":"application/json"};r?s["x-mcp-".concat(r,"-authorization")]=a:s["x-mcp-auth"]=a;let l=await fetch(n,{method:"POST",headers:s,body:JSON.stringify({name:t,arguments:o})});if(!l.ok){let e=await l.text();throw p(e),Error("Network response was not ok")}let i=await l.json();return console.log("MCP tool call response:",i),i}catch(e){throw console.error("Failed to call MCP tool:",e),e}},tP=async(e,t)=>{try{let o=c?"".concat(c,"/tag/new"):"/tag/new",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();await p(e);return}return await a.json()}catch(e){throw console.error("Error creating tag:",e),e}},tx=async(e,t)=>{try{let o=c?"".concat(c,"/tag/update"):"/tag/update",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();await p(e);return}return await a.json()}catch(e){throw console.error("Error updating tag:",e),e}},tO=async(e,t)=>{try{let o=c?"".concat(c,"/tag/info"):"/tag/info",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({names:t})});if(!a.ok){let e=await a.text();return await p(e),{}}return await a.json()}catch(e){throw console.error("Error getting tag info:",e),e}},tB=async e=>{try{let t=c?"".concat(c,"/tag/list"):"/tag/list",o=await fetch(t,{method:"GET",headers:{Authorization:"Bearer ".concat(e)}});if(!o.ok){let e=await o.text();return await p(e),{}}return await o.json()}catch(e){throw console.error("Error listing tags:",e),e}},tG=async(e,t)=>{try{let o=c?"".concat(c,"/tag/delete"):"/tag/delete",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({name:t})});if(!a.ok){let e=await a.text();await p(e);return}return await a.json()}catch(e){throw console.error("Error deleting tag:",e),e}},tJ=async e=>{try{let t=c?"".concat(c,"/get/default_team_settings"):"/get/default_team_settings";console.log("Fetching default team settings from:",t);let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched default team settings:",a),a}catch(e){throw console.error("Failed to fetch default team settings:",e),e}},tU=async(e,t)=>{try{let o=c?"".concat(c,"/update/default_team_settings"):"/update/default_team_settings";console.log("Updating default team settings:",t);let r=await fetch(o,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.text();throw p(e),Error("Network response was not ok")}let n=await r.json();return console.log("Updated default team settings:",n),a.ZP.success("Default team settings updated successfully"),n}catch(e){throw console.error("Failed to update default team settings:",e),e}},tA=async(e,t)=>{try{let o=c?"".concat(c,"/team/permissions_list?team_id=").concat(t):"/team/permissions_list?team_id=".concat(t),a=await fetch(o,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log("Team permissions response:",r),r}catch(e){throw console.error("Failed to get team permissions:",e),e}},tI=async(e,t,o)=>{try{let a=c?"".concat(c,"/team/permissions_update"):"/team/permissions_update",r=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({team_id:t,team_member_permissions:o})});if(!r.ok){let e=await r.text();throw p(e),Error("Network response was not ok")}let n=await r.json();return console.log("Team permissions response:",n),n}catch(e){throw console.error("Failed to update team permissions:",e),e}},tR=async(e,t)=>{try{let o=c?"".concat(c,"/spend/logs/session/ui?session_id=").concat(encodeURIComponent(t)):"/spend/logs/session/ui?session_id=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to fetch session logs:",e),e}},tM=async(e,t)=>{try{let o=c?"".concat(c,"/vector_store/new"):"/vector_store/new",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to create vector store")}return await a.json()}catch(e){throw console.error("Error creating vector store:",e),e}},tz=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let t=c?"".concat(c,"/vector_store/list"):"/vector_store/list",o=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!o.ok){let e=await o.json();throw Error(e.detail||"Failed to list vector stores")}return await o.json()}catch(e){throw console.error("Error listing vector stores:",e),e}},tL=async(e,t)=>{try{let o=c?"".concat(c,"/vector_store/delete"):"/vector_store/delete",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({vector_store_id:t})});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to delete vector store")}return await a.json()}catch(e){throw console.error("Error deleting vector store:",e),e}},tV=async(e,t)=>{try{let o=c?"".concat(c,"/vector_store/info"):"/vector_store/info",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({vector_store_id:t})});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to get vector store info")}return await a.json()}catch(e){throw console.error("Error getting vector store info:",e),e}},tD=async(e,t)=>{try{let o=c?"".concat(c,"/vector_store/update"):"/vector_store/update",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Failed to update vector store")}return await a.json()}catch(e){throw console.error("Error updating vector store:",e),e}},tH=async e=>{try{let t=c?"".concat(c,"/email/event_settings"):"/email/event_settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Failed to get email event settings")}let a=await o.json();return console.log("Email event settings response:",a),a}catch(e){throw console.error("Failed to get email event settings:",e),e}},tq=async(e,t)=>{try{let o=c?"".concat(c,"/email/event_settings"):"/email/event_settings",a=await fetch(o,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw p(e),Error("Failed to update email event settings")}let r=await a.json();return console.log("Update email event settings response:",r),r}catch(e){throw console.error("Failed to update email event settings:",e),e}},tZ=async e=>{try{let t=c?"".concat(c,"/email/event_settings/reset"):"/email/event_settings/reset",o=await fetch(t,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Failed to reset email event settings")}let a=await o.json();return console.log("Reset email event settings response:",a),a}catch(e){throw console.error("Failed to reset email event settings:",e),e}},tK=async(e,t)=>{try{let o=c?"".concat(c,"/guardrails/").concat(t):"/guardrails/".concat(t),a=await fetch(o,{method:"DELETE",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error(e)}let r=await a.json();return console.log("Delete guardrail response:",r),r}catch(e){throw console.error("Failed to delete guardrail:",e),e}},tY=async e=>{try{let t=c?"".concat(c,"/guardrails/ui/add_guardrail_settings"):"/guardrails/ui/add_guardrail_settings",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Failed to get guardrail UI settings")}let a=await o.json();return console.log("Guardrail UI settings response:",a),a}catch(e){throw console.error("Failed to get guardrail UI settings:",e),e}},tW=async e=>{try{let t=c?"".concat(c,"/guardrails/ui/provider_specific_params"):"/guardrails/ui/provider_specific_params",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Failed to get guardrail provider specific parameters")}let a=await o.json();return console.log("Guardrail provider specific params response:",a),a}catch(e){throw console.error("Failed to get guardrail provider specific parameters:",e),e}},tQ=async(e,t)=>{try{let o=c?"".concat(c,"/guardrails/").concat(t,"/info"):"/guardrails/".concat(t,"/info"),a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Failed to get guardrail info")}let r=await a.json();return console.log("Guardrail info response:",r),r}catch(e){throw console.error("Failed to get guardrail info:",e),e}},tX=async(e,t,o)=>{try{let a=c?"".concat(c,"/guardrails/").concat(t):"/guardrails/".concat(t),r=await fetch(a,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!r.ok){let e=await r.text();throw p(e),Error("Failed to update guardrail")}let n=await r.json();return console.log("Update guardrail response:",n),n}catch(e){throw console.error("Failed to update guardrail:",e),e}},t$=async e=>{try{let t=c?"".concat(c,"/get/sso_settings"):"/get/sso_settings";console.log("Fetching SSO configuration from:",t);let o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok){let e=await o.text();throw p(e),Error("Network response was not ok")}let a=await o.json();return console.log("Fetched SSO configuration:",a),a}catch(e){throw console.error("Failed to fetch SSO configuration:",e),e}},t0=async(e,t)=>{try{let o=c?"".concat(c,"/update/sso_settings"):"/update/sso_settings";console.log("Updating SSO configuration:",t);let a=await fetch(o,{method:"PATCH",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=await a.json();return console.log("Updated SSO configuration:",r),r}catch(e){throw console.error("Failed to update SSO configuration:",e),e}},t1=async(e,t,o,a,r)=>{try{let t=c?"".concat(c,"/audit"):"/audit",o=new URLSearchParams;a&&o.append("page",a.toString()),r&&o.append("page_size",r.toString());let n=o.toString();n&&(t+="?".concat(n));let s=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok){let e=await s.text();throw p(e),Error("Network response was not ok")}return await s.json()}catch(e){throw console.error("Failed to fetch audit logs:",e),e}},t3=async e=>{try{let t=c?"".concat(c,"/user/available_users"):"/user/available_users",o=await fetch(t,{method:"GET",headers:{[u]:"Bearer ".concat(e)}});if(!o.ok){if(404===o.status)return null;let e=await o.text();throw p(e),Error("Network response was not ok")}return await o.json()}catch(e){throw console.error("Failed to fetch remaining users:",e),e}},t2=async(e,t,o)=>{try{let r=c?"".concat(c,"/config/pass_through_endpoint/").concat(encodeURIComponent(t)):"/config/pass_through_endpoint/".concat(encodeURIComponent(t)),n=await fetch(r,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify(o)});if(!n.ok){let e=await n.text();throw p(e),Error("Network response was not ok")}let s=await n.json();return a.ZP.success("Pass through endpoint updated successfully"),s}catch(e){throw console.error("Failed to update pass through endpoint:",e),e}},t4=async(e,t)=>{try{let o=c?"".concat(c,"/config/pass_through_endpoint?endpoint_id=").concat(encodeURIComponent(t)):"/config/pass_through_endpoint?endpoint_id=".concat(encodeURIComponent(t)),a=await fetch(o,{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}let r=(await a.json()).endpoints;if(!r||0===r.length)throw Error("Pass through endpoint not found");return r[0]}catch(e){throw console.error("Failed to get pass through endpoint info:",e),e}},t5=async(e,t)=>{try{let o=c?"".concat(c,"/config/callback/delete"):"/config/callback/delete",a=await fetch(o,{method:"POST",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({callback_name:t})});if(!a.ok){let e=await a.text();throw p(e),Error("Network response was not ok")}return await a.json()}catch(e){throw console.error("Failed to delete specific callback:",e),e}},t6=async e=>{let t=l(),o=await fetch("".concat(t,"/v1/mcp/tools"),{method:"GET",headers:{[u]:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!o.ok)throw Error("HTTP error! status: ".concat(o.status));return await o.json()},t9=async(e,t)=>{try{console.log("Testing MCP connection with config:",JSON.stringify(t));let a=c?"".concat(c,"/mcp-rest/test/connection"):"/mcp-rest/test/connection",r=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json",[u]:"Bearer ".concat(e)},body:JSON.stringify(t)}),n=r.headers.get("content-type");if(!n||!n.includes("application/json")){let e=await r.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(r.status,": ").concat(r.statusText,"). Check network tab for details."))}let s=await r.json();if(!r.ok||"error"===s.status){if("error"===s.status);else{var o;return{status:"error",message:(null===(o=s.error)||void 0===o?void 0:o.message)||"MCP connection test failed: ".concat(r.status," ").concat(r.statusText)}}}return s}catch(e){throw console.error("MCP connection test error:",e),e}},t7=async(e,t)=>{try{console.log("Testing MCP tools list with config:",JSON.stringify(t));let o=c?"".concat(c,"/mcp-rest/test/tools/list"):"/mcp-rest/test/tools/list",a=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json",[u]:"Bearer ".concat(e)},body:JSON.stringify(t)}),r=a.headers.get("content-type");if(!r||!r.includes("application/json")){let e=await a.text();throw console.error("Received non-JSON response:",e),Error("Received non-JSON response (".concat(a.status,": ").concat(a.statusText,"). Check network tab for details."))}let n=await a.json();if((!a.ok||n.error)&&!n.error)return{tools:[],error:"request_failed",message:n.message||"MCP tools list failed: ".concat(a.status," ").concat(a.statusText)};return n}catch(e){throw console.error("MCP tools list test error:",e),e}},t8=async(e,t,o)=>{try{let a="".concat(l(),"/v1/vector_stores/").concat(t,"/search"),r=await fetch(a,{method:"POST",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({query:o})});if(!r.ok){let e=await r.text();return await p(e),null}return await r.json()}catch(e){throw console.error("Error testing vector store search:",e),e}}},3914:function(e,t,o){function a(){let e=window.location.hostname,t=["Lax","Strict","None"];["/","/ui"].forEach(o=>{document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,";"),document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,"; domain=").concat(e,";"),t.forEach(t=>{let a="None"===t?" Secure;":"";document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,"; SameSite=").concat(t,";").concat(a),document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(o,"; domain=").concat(e,"; SameSite=").concat(t,";").concat(a)})}),console.log("After clearing cookies:",document.cookie)}function r(e){let t=document.cookie.split("; ").find(t=>t.startsWith(e+"="));return t?t.split("=")[1]:null}o.d(t,{b:function(){return a},e:function(){return r}})}}]);
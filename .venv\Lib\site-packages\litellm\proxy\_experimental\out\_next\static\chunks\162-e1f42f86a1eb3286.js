"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{36724:function(e,t,n){n.d(t,{Dx:function(){return i.Z},Zb:function(){return a.Z},xv:function(){return r.Z},zx:function(){return s.Z}});var s=n(20831),a=n(12514),r=n(84264),i=n(96761)},19130:function(e,t,n){n.d(t,{RM:function(){return a.Z},SC:function(){return o.Z},iA:function(){return s.Z},pj:function(){return r.Z},ss:function(){return i.Z},xs:function(){return l.Z}});var s=n(21626),a=n(97214),r=n(28241),i=n(58834),l=n(69552),o=n(71876)},88658:function(e,t,n){n.d(t,{L:function(){return a}});var s=n(49817);let a=e=>{let t;let{apiKeySource:n,accessToken:a,apiKey:r,inputMessage:i,chatHistory:l,selectedTags:o,selectedVectorStores:c,selectedGuardrails:d,endpointType:m,selectedModel:p,selectedSdk:u}=e,g="session"===n?a:r,x=window.location.origin,h=(i||"Your prompt here").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n"),f=l.filter(e=>!e.isImage).map(e=>{let{role:t,content:n}=e;return{role:t,content:n}}),_={};o.length>0&&(_.tags=o),c.length>0&&(_.vector_stores=c),d.length>0&&(_.guardrails=d);let b=p||"your-model-name",j="azure"===u?'import openai\n\nclient = openai.AzureOpenAI(\n	api_key="'.concat(g||"YOUR_LITELLM_API_KEY",'",\n	azure_endpoint="').concat(x,'",\n	api_version="2024-02-01"\n)'):'import openai\n\nclient = openai.OpenAI(\n	api_key="'.concat(g||"YOUR_LITELLM_API_KEY",'",\n	base_url="').concat(x,'"\n)');switch(m){case s.KP.CHAT:{let e=Object.keys(_).length>0,n="";if(e){let e=JSON.stringify({metadata:_},null,2).split("\n").map(e=>" ".repeat(4)+e).join("\n").trim();n=",\n    extra_body=".concat(e)}t='\n# request sent to model set on litellm proxy, `litellm --model`\nresponse = client.chat.completions.create(\n	model="'.concat(b,'",\n	messages = ').concat(JSON.stringify(f,null,4)).concat(n,"\n)\n\nprint(response)\n");break}case s.KP.RESPONSES:{let e=Object.keys(_).length>0,n="";if(e){let e=JSON.stringify({metadata:_},null,2).split("\n").map(e=>" ".repeat(4)+e).join("\n").trim();n=",\n    extra_body=".concat(e)}t='\n# request sent to model set on litellm proxy, `litellm --model`\nresponse = client.responses.create(\n	model="'.concat(b,'",\n	messages = ').concat(JSON.stringify(f,null,4)).concat(n,"\n)\n\nprint(response)\n");break}case s.KP.IMAGE:t="azure"===u?"\n# NOTE: The Azure SDK does not have a direct equivalent to the multi-modal 'responses.create' method shown for OpenAI.\n# This snippet uses 'client.images.generate' and will create a new image based on your prompt.\n# It does not use the uploaded image, as 'client.images.generate' does not support image inputs in this context.\nimport os\nimport requests\nimport json\nimport time\nfrom PIL import Image\n\nresult = client.images.generate(\n	model=\"".concat(b,'",\n	prompt="').concat(i,'",\n	n=1\n)\n\njson_response = json.loads(result.model_dump_json())\n\n# Set the directory for the stored image\nimage_dir = os.path.join(os.curdir, \'images\')\n\n# If the directory doesn\'t exist, create it\nif not os.path.isdir(image_dir):\n	os.mkdir(image_dir)\n\n# Initialize the image path\nimage_filename = f"generated_image_{int(time.time())}.png"\nimage_path = os.path.join(image_dir, image_filename)\n\ntry:\n	# Retrieve the generated image\n	if json_response.get("data") && len(json_response["data"]) > 0 && json_response["data"][0].get("url"):\n			image_url = json_response["data"][0]["url"]\n			generated_image = requests.get(image_url).content\n			with open(image_path, "wb") as image_file:\n					image_file.write(generated_image)\n\n			print(f"Image saved to {image_path}")\n			# Display the image\n			image = Image.open(image_path)\n			image.show()\n	else:\n			print("Could not find image URL in response.")\n			print("Full response:", json_response)\nexcept Exception as e:\n	print(f"An error occurred: {e}")\n	print("Full response:", json_response)\n'):"\nimport base64\nimport os\nimport time\nimport json\nfrom PIL import Image\nimport requests\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n	with open(image_path, \"rb\") as image_file:\n			return base64.b64encode(image_file.read()).decode('utf-8')\n\n# Helper function to create a file (simplified for this example)\ndef create_file(image_path):\n	# In a real implementation, this would upload the file to OpenAI\n	# For this example, we'll just return a placeholder ID\n	return f\"file_{os.path.basename(image_path).replace('.', '_')}\"\n\n# The prompt entered by the user\nprompt = \"".concat(h,'"\n\n# Encode images to base64\nbase64_image1 = encode_image("body-lotion.png")\nbase64_image2 = encode_image("soap.png")\n\n# Create file IDs\nfile_id1 = create_file("body-lotion.png")\nfile_id2 = create_file("incense-kit.png")\n\nresponse = client.responses.create(\n	model="').concat(b,'",\n	input=[\n			{\n					"role": "user",\n					"content": [\n							{"type": "input_text", "text": prompt},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image1}",\n							},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image2}",\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id1,\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id2,\n							}\n					],\n			}\n	],\n	tools=[{"type": "image_generation"}],\n)\n\n# Process the response\nimage_generation_calls = [\n	output\n	for output in response.output\n	if output.type == "image_generation_call"\n]\n\nimage_data = [output.result for output in image_generation_calls]\n\nif image_data:\n	image_base64 = image_data[0]\n	image_filename = f"edited_image_{int(time.time())}.png"\n	with open(image_filename, "wb") as f:\n			f.write(base64.b64decode(image_base64))\n	print(f"Image saved to {image_filename}")\nelse:\n	# If no image is generated, there might be a text response with an explanation\n	text_response = [output.text for output in response.output if hasattr(output, \'text\')]\n	if text_response:\n			print("No image generated. Model response:")\n			print("\\n".join(text_response))\n	else:\n			print("No image data found in response.")\n	print("Full response for debugging:")\n	print(response)\n');break;case s.KP.IMAGE_EDITS:t="azure"===u?'\nimport base64\nimport os\nimport time\nimport json\nfrom PIL import Image\nimport requests\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n	with open(image_path, "rb") as image_file:\n			return base64.b64encode(image_file.read()).decode(\'utf-8\')\n\n# The prompt entered by the user\nprompt = "'.concat(h,'"\n\n# Encode images to base64\nbase64_image1 = encode_image("body-lotion.png")\nbase64_image2 = encode_image("soap.png")\n\n# Create file IDs\nfile_id1 = create_file("body-lotion.png")\nfile_id2 = create_file("incense-kit.png")\n\nresponse = client.responses.create(\n	model="').concat(b,'",\n	input=[\n			{\n					"role": "user",\n					"content": [\n							{"type": "input_text", "text": prompt},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image1}",\n							},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image2}",\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id1,\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id2,\n							}\n					],\n			}\n	],\n	tools=[{"type": "image_generation"}],\n)\n\n# Process the response\nimage_generation_calls = [\n	output\n	for output in response.output\n	if output.type == "image_generation_call"\n]\n\nimage_data = [output.result for output in image_generation_calls]\n\nif image_data:\n	image_base64 = image_data[0]\n	image_filename = f"edited_image_{int(time.time())}.png"\n	with open(image_filename, "wb") as f:\n			f.write(base64.b64decode(image_base64))\n	print(f"Image saved to {image_filename}")\nelse:\n	# If no image is generated, there might be a text response with an explanation\n	text_response = [output.text for output in response.output if hasattr(output, \'text\')]\n	if text_response:\n			print("No image generated. Model response:")\n			print("\\n".join(text_response))\n	else:\n			print("No image data found in response.")\n	print("Full response for debugging:")\n	print(response)\n'):"\nimport base64\nimport os\nimport time\n\n# Helper function to encode images to base64\ndef encode_image(image_path):\n	with open(image_path, \"rb\") as image_file:\n			return base64.b64encode(image_file.read()).decode('utf-8')\n\n# Helper function to create a file (simplified for this example)\ndef create_file(image_path):\n	# In a real implementation, this would upload the file to OpenAI\n	# For this example, we'll just return a placeholder ID\n	return f\"file_{os.path.basename(image_path).replace('.', '_')}\"\n\n# The prompt entered by the user\nprompt = \"".concat(h,'"\n\n# Encode images to base64\nbase64_image1 = encode_image("body-lotion.png")\nbase64_image2 = encode_image("soap.png")\n\n# Create file IDs\nfile_id1 = create_file("body-lotion.png")\nfile_id2 = create_file("incense-kit.png")\n\nresponse = client.responses.create(\n	model="').concat(b,'",\n	input=[\n			{\n					"role": "user",\n					"content": [\n							{"type": "input_text", "text": prompt},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image1}",\n							},\n							{\n									"type": "input_image",\n									"image_url": f"data:image/jpeg;base64,{base64_image2}",\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id1,\n							},\n							{\n									"type": "input_image",\n									"file_id": file_id2,\n							}\n					],\n			}\n	],\n	tools=[{"type": "image_generation"}],\n)\n\n# Process the response\nimage_generation_calls = [\n	output\n	for output in response.output\n	if output.type == "image_generation_call"\n]\n\nimage_data = [output.result for output in image_generation_calls]\n\nif image_data:\n	image_base64 = image_data[0]\n	image_filename = f"edited_image_{int(time.time())}.png"\n	with open(image_filename, "wb") as f:\n			f.write(base64.b64decode(image_base64))\n	print(f"Image saved to {image_filename}")\nelse:\n	# If no image is generated, there might be a text response with an explanation\n	text_response = [output.text for output in response.output if hasattr(output, \'text\')]\n	if text_response:\n			print("No image generated. Model response:")\n			print("\\n".join(text_response))\n	else:\n			print("No image data found in response.")\n	print("Full response for debugging:")\n	print(response)\n');break;default:t="\n# Code generation for this endpoint is not implemented yet."}return"".concat(j,"\n").concat(t)}},49817:function(e,t,n){var s,a,r,i;n.d(t,{KP:function(){return a},vf:function(){return o}}),(r=s||(s={})).IMAGE_GENERATION="image_generation",r.CHAT="chat",r.RESPONSES="responses",r.IMAGE_EDITS="image_edits",r.ANTHROPIC_MESSAGES="anthropic_messages",(i=a||(a={})).IMAGE="image",i.CHAT="chat",i.RESPONSES="responses",i.IMAGE_EDITS="image_edits",i.ANTHROPIC_MESSAGES="anthropic_messages";let l={image_generation:"image",chat:"chat",responses:"responses",image_edits:"image_edits",anthropic_messages:"anthropic_messages"},o=e=>{if(console.log("getEndpointType:",e),Object.values(s).includes(e)){let t=l[e];return console.log("endpointType:",t),t}return"chat"}},8048:function(e,t,n){n.d(t,{C:function(){return m}});var s=n(57437),a=n(71594),r=n(24525),i=n(2265),l=n(19130),o=n(44633),c=n(86462),d=n(49084);function m(e){let{data:t=[],columns:n,isLoading:m=!1,table:p,defaultSorting:u=[]}=e,[g,x]=i.useState(u),[h]=i.useState("onChange"),[f,_]=i.useState({}),[b,j]=i.useState({}),v=(0,a.b7)({data:t,columns:n,state:{sorting:g,columnSizing:f,columnVisibility:b},columnResizeMode:h,onSortingChange:x,onColumnSizingChange:_,onColumnVisibilityChange:j,getCoreRowModel:(0,r.sC)(),getSortedRowModel:(0,r.tj)(),enableSorting:!0,enableColumnResizing:!0,defaultColumn:{minSize:40,maxSize:500}});return i.useEffect(()=>{p&&(p.current=v)},[v,p]),(0,s.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"relative min-w-full",children:(0,s.jsxs)(l.iA,{className:"[&_td]:py-2 [&_th]:py-2 w-full",children:[(0,s.jsx)(l.ss,{children:v.getHeaderGroups().map(e=>(0,s.jsx)(l.SC,{children:e.headers.map(e=>{var t;return(0,s.jsxs)(l.xs,{className:"py-1 h-8 relative ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)] z-20 w-[120px] ml-8":""," ").concat((null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.className)||""),style:{width:"actions"===e.id?120:e.getSize(),position:"actions"===e.id?"sticky":"relative",right:"actions"===e.id?0:"auto"},onClick:e.column.getCanSort()?e.column.getToggleSortingHandler():void 0,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,s.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,a.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&e.column.getCanSort()&&(0,s.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,s.jsx)(o.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,s.jsx)(c.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,s.jsx)(d.Z,{className:"h-4 w-4 text-gray-400"})})]}),e.column.getCanResize()&&(0,s.jsx)("div",{onMouseDown:e.getResizeHandler(),onTouchStart:e.getResizeHandler(),className:"absolute right-0 top-0 h-full w-2 cursor-col-resize select-none touch-none ".concat(e.column.getIsResizing()?"bg-blue-500":"hover:bg-blue-200")})]},e.id)})},e.id))}),(0,s.jsx)(l.RM,{children:m?(0,s.jsx)(l.SC,{children:(0,s.jsx)(l.pj,{colSpan:n.length,className:"h-8 text-center",children:(0,s.jsx)("div",{className:"text-center text-gray-500",children:(0,s.jsx)("p",{children:"\uD83D\uDE85 Loading models..."})})})}):v.getRowModel().rows.length>0?v.getRowModel().rows.map(e=>(0,s.jsx)(l.SC,{children:e.getVisibleCells().map(e=>{var t;return(0,s.jsx)(l.pj,{className:"py-0.5 ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)] z-20 w-[120px] ml-8":""," ").concat((null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.className)||""),style:{width:"actions"===e.column.id?120:e.column.getSize(),position:"actions"===e.column.id?"sticky":"relative",right:"actions"===e.column.id?0:"auto"},children:(0,a.ie)(e.column.columnDef.cell,e.getContext())},e.id)})},e.id)):(0,s.jsx)(l.SC,{children:(0,s.jsx)(l.pj,{colSpan:n.length,className:"h-8 text-center",children:(0,s.jsx)("div",{className:"text-center text-gray-500",children:(0,s.jsx)("p",{children:"No models found"})})})})})]})})})})}},65373:function(e,t,n){n.d(t,{Z:function(){return h}});var s=n(57437),a=n(27648),r=n(2265),i=n(89970),l=n(80795),o=n(19250),c=n(15883),d=n(46346),m=n(57400),p=n(91870),u=n(40428),g=n(3914);let x=async e=>{if(!e)return null;try{return await (0,o.getProxyUISettings)(e)}catch(e){return console.error("Error fetching proxy settings:",e),null}};var h=e=>{let{userID:t,userEmail:n,userRole:h,premiumUser:f,proxySettings:_,setProxySettings:b,accessToken:j,isPublicPage:v=!1}=e,y=(0,o.getProxyBaseUrl)(),[N,w]=(0,r.useState)("");(0,r.useEffect)(()=>{(async()=>{if(j){let e=await x(j);console.log("response from fetchProxySettings",e),e&&b(e)}})()},[j]),(0,r.useEffect)(()=>{w((null==_?void 0:_.PROXY_LOGOUT_URL)||"")},[_]);let S=[{key:"user-info",label:(0,s.jsxs)("div",{className:"px-3 py-3 border-b border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.Z,{className:"mr-2 text-gray-700"}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-900",children:t})]}),f?(0,s.jsx)(i.Z,{title:"Premium User",placement:"left",children:(0,s.jsxs)("div",{className:"flex items-center bg-gradient-to-r from-amber-500 to-yellow-500 text-white px-2 py-0.5 rounded-full cursor-help",children:[(0,s.jsx)(d.Z,{className:"mr-1 text-xs"}),(0,s.jsx)("span",{className:"text-xs font-medium",children:"Premium"})]})}):(0,s.jsx)(i.Z,{title:"Upgrade to Premium for advanced features",placement:"left",children:(0,s.jsxs)("div",{className:"flex items-center bg-gray-100 text-gray-500 px-2 py-0.5 rounded-full cursor-help",children:[(0,s.jsx)(d.Z,{className:"mr-1 text-xs"}),(0,s.jsx)("span",{className:"text-xs font-medium",children:"Standard"})]})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)(m.Z,{className:"mr-2 text-gray-400 text-xs"}),(0,s.jsx)("span",{className:"text-gray-500 text-xs",children:"Role"}),(0,s.jsx)("span",{className:"ml-auto text-gray-700 font-medium",children:h})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)(p.Z,{className:"mr-2 text-gray-400 text-xs"}),(0,s.jsx)("span",{className:"text-gray-500 text-xs",children:"Email"}),(0,s.jsx)("span",{className:"ml-auto text-gray-700 font-medium truncate max-w-[150px]",title:n||"Unknown",children:n||"Unknown"})]})]})]})},{key:"logout",label:(0,s.jsxs)("div",{className:"flex items-center py-2 px-3 hover:bg-gray-50 rounded-md mx-1 my-1",onClick:()=>{(0,g.b)(),window.location.href=N},children:[(0,s.jsx)(u.Z,{className:"mr-3 text-gray-600"}),(0,s.jsx)("span",{className:"text-gray-800",children:"Logout"})]})}];return(0,s.jsx)("nav",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)("div",{className:"flex items-center h-12 px-4",children:[(0,s.jsx)("div",{className:"flex items-center flex-shrink-0",children:(0,s.jsx)(a.default,{href:"/",className:"flex items-center",children:(0,s.jsx)("img",{src:y+"/get_image",alt:"LiteLLM Brand",className:"h-8 w-auto"})})}),(0,s.jsxs)("div",{className:"flex items-center space-x-5 ml-auto",children:[(0,s.jsx)("a",{href:"https://docs.litellm.ai/docs/",target:"_blank",rel:"noopener noreferrer",className:"text-[13px] text-gray-600 hover:text-gray-900 transition-colors",children:"Docs"}),!v&&(0,s.jsx)(l.Z,{menu:{items:S,className:"min-w-[200px]",style:{padding:"8px",marginTop:"8px",borderRadius:"12px",boxShadow:"0 4px 24px rgba(0, 0, 0, 0.08)"}},overlayStyle:{minWidth:"200px"},children:(0,s.jsxs)("button",{className:"inline-flex items-center text-[13px] text-gray-600 hover:text-gray-900 transition-colors",children:["User",(0,s.jsx)("svg",{className:"ml-1 w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M19 9l-7 7-7-7"})})]})})]})]})})})}},42673:function(e,t,n){var s,a;n.d(t,{Cl:function(){return s},bK:function(){return d},cd:function(){return l},dr:function(){return o},fK:function(){return r},ph:function(){return c}}),n(2265),(a=s||(s={})).OpenAI="OpenAI",a.OpenAI_Compatible="OpenAI-Compatible Endpoints (Together AI, etc.)",a.OpenAI_Text="OpenAI Text Completion",a.OpenAI_Text_Compatible="OpenAI-Compatible Text Completion Models (Together AI, etc.)",a.Azure="Azure",a.Azure_AI_Studio="Azure AI Foundry (Studio)",a.Anthropic="Anthropic",a.Vertex_AI="Vertex AI (Anthropic, Gemini, etc.)",a.Google_AI_Studio="Google AI Studio",a.Bedrock="Amazon Bedrock",a.Groq="Groq",a.MistralAI="Mistral AI",a.Deepseek="Deepseek",a.Cohere="Cohere",a.Databricks="Databricks",a.Ollama="Ollama",a.xAI="xAI",a.AssemblyAI="AssemblyAI",a.Cerebras="Cerebras",a.Sambanova="Sambanova",a.Perplexity="Perplexity",a.TogetherAI="TogetherAI",a.Openrouter="Openrouter",a.FireworksAI="Fireworks AI",a.Triton="Triton",a.Deepgram="Deepgram",a.ElevenLabs="ElevenLabs";let r={OpenAI:"openai",OpenAI_Text:"text-completion-openai",Azure:"azure",Azure_AI_Studio:"azure_ai",Anthropic:"anthropic",Google_AI_Studio:"gemini",Bedrock:"bedrock",Groq:"groq",MistralAI:"mistral",Cohere:"cohere",OpenAI_Compatible:"openai",OpenAI_Text_Compatible:"text-completion-openai",Vertex_AI:"vertex_ai",Databricks:"databricks",xAI:"xai",Deepseek:"deepseek",Ollama:"ollama",AssemblyAI:"assemblyai",Cerebras:"cerebras",Sambanova:"sambanova",Perplexity:"perplexity",TogetherAI:"together_ai",Openrouter:"openrouter",FireworksAI:"fireworks_ai",Triton:"triton",Deepgram:"deepgram",ElevenLabs:"elevenlabs"},i="/ui/assets/logos/",l={Anthropic:"".concat(i,"anthropic.svg"),AssemblyAI:"".concat(i,"assemblyai_small.png"),Azure:"".concat(i,"microsoft_azure.svg"),"Azure AI Foundry (Studio)":"".concat(i,"microsoft_azure.svg"),"Amazon Bedrock":"".concat(i,"bedrock.svg"),Cerebras:"".concat(i,"cerebras.svg"),Cohere:"".concat(i,"cohere.svg"),Databricks:"".concat(i,"databricks.svg"),Deepseek:"".concat(i,"deepseek.svg"),"Fireworks AI":"".concat(i,"fireworks.svg"),Groq:"".concat(i,"groq.svg"),"Google AI Studio":"".concat(i,"google.svg"),"Mistral AI":"".concat(i,"mistral.svg"),Ollama:"".concat(i,"ollama.svg"),OpenAI:"".concat(i,"openai_small.svg"),"OpenAI Text Completion":"".concat(i,"openai_small.svg"),"OpenAI-Compatible Text Completion Models (Together AI, etc.)":"".concat(i,"openai_small.svg"),"OpenAI-Compatible Endpoints (Together AI, etc.)":"".concat(i,"openai_small.svg"),Openrouter:"".concat(i,"openrouter.svg"),Perplexity:"".concat(i,"perplexity-ai.svg"),Sambanova:"".concat(i,"sambanova.svg"),TogetherAI:"".concat(i,"togetherai.svg"),"Vertex AI (Anthropic, Gemini, etc.)":"".concat(i,"google.svg"),xAI:"".concat(i,"xai.svg"),Triton:"".concat(i,"nvidia_triton.png"),Deepgram:"".concat(i,"deepgram.png"),ElevenLabs:"".concat(i,"elevenlabs.png")},o=e=>{if(!e)return{logo:"",displayName:"-"};if("gemini"===e.toLowerCase()){let e="Google AI Studio";return{logo:l[e],displayName:e}}let t=Object.keys(r).find(t=>r[t].toLowerCase()===e.toLowerCase());if(!t)return{logo:"",displayName:e};let n=s[t];return{logo:l[n],displayName:n}},c=e=>"Vertex AI (Anthropic, Gemini, etc.)"===e?"gemini-pro":"Anthropic"==e||"Amazon Bedrock"==e?"claude-3-opus":"Google AI Studio"==e?"gemini-pro":"Azure AI Foundry (Studio)"==e?"azure_ai/command-r-plus":"Azure"==e?"azure/my-deployment":"gpt-3.5-turbo",d=(e,t)=>{console.log("Provider key: ".concat(e));let n=r[e];console.log("Provider mapped to: ".concat(n));let s=[];return e&&"object"==typeof t&&(Object.entries(t).forEach(e=>{let[t,a]=e;null!==a&&"object"==typeof a&&"litellm_provider"in a&&(a.litellm_provider===n||a.litellm_provider.includes(n))&&s.push(t)}),"Cohere"==e&&(console.log("Adding cohere chat models"),Object.entries(t).forEach(e=>{let[t,n]=e;null!==n&&"object"==typeof n&&"litellm_provider"in n&&"cohere_chat"===n.litellm_provider&&s.push(t)}))),s}},72162:function(e,t,n){var s=n(57437),a=n(2265),r=n(19250),i=n(8048),l=n(36724),o=n(41021),c=n(89970),d=n(3810),m=n(52787),p=n(91679),u=n(3477),g=n(17732),x=n(33245),h=n(78867),f=n(88658),_=n(49817),b=n(42673),j=n(65373);t.Z=e=>{var t,n;let{accessToken:v}=e,[y,N]=(0,a.useState)(null),[w,S]=(0,a.useState)("LiteLLM Gateway"),[A,I]=(0,a.useState)(null),[k,C]=(0,a.useState)(""),[O,E]=(0,a.useState)({}),[T,M]=(0,a.useState)(!0),[z,D]=(0,a.useState)(""),[L,P]=(0,a.useState)([]),[Z,R]=(0,a.useState)([]),[G,H]=(0,a.useState)([]),[K,F]=(0,a.useState)("I'm alive! ✓"),[U,q]=(0,a.useState)(!1),[B,V]=(0,a.useState)(null),[W,Y]=(0,a.useState)({}),J=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=async()=>{try{M(!0);let e=await (0,r.modelHubPublicModelsCall)();console.log("ModelHubData:",e),N(e)}catch(e){console.error("There was an error fetching the public model data",e),F("Service unavailable")}finally{M(!1)}};(async()=>{let e=await (0,r.getPublicModelHubInfo)();console.log("Public Model Hub Info:",e),S(e.docs_title),I(e.custom_docs_description),C(e.litellm_version),E(e.useful_links||{})})(),e()},[]),(0,a.useEffect)(()=>{},[z,L,Z,G]);let $=(0,a.useMemo)(()=>{if(!y)return[];let e=y;if(z.trim()){let t=z.toLowerCase(),n=t.split(/\s+/),s=y.filter(e=>{let s=e.model_group.toLowerCase();return!!s.includes(t)||n.every(e=>s.includes(e))});s.length>0&&(e=s.sort((e,n)=>{let s=e.model_group.toLowerCase(),a=n.model_group.toLowerCase(),r=s===t?1e3:0,i=a===t?1e3:0,l=s.startsWith(t)?100:0,o=a.startsWith(t)?100:0,c=t.split(/\s+/).every(e=>s.includes(e))?50:0,d=t.split(/\s+/).every(e=>a.includes(e))?50:0,m=s.length;return i+o+d+(1e3-a.length)-(r+l+c+(1e3-m))}))}return e.filter(e=>{let t=0===L.length||L.some(t=>e.providers.includes(t)),n=0===Z.length||Z.includes(e.mode||""),s=0===G.length||Object.entries(e).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).some(e=>{let[t]=e,n=t.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");return G.includes(n)});return t&&n&&s})},[y,z,L,Z,G]),X=e=>{V(e),q(!0)},Q=e=>{navigator.clipboard.writeText(e),o.ZP.success("Copied to clipboard!")},ee=e=>e.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),et=e=>Object.entries(e).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).map(e=>{let[t]=e;return t}),en=e=>"$".concat((1e6*e).toFixed(4)),es=e=>e?e>=1e3?"".concat((e/1e3).toFixed(0),"K"):e.toString():"N/A",ea=(e,t)=>{let n=[];return e&&n.push("RPM: ".concat(e.toLocaleString())),t&&n.push("TPM: ".concat(t.toLocaleString())),n.length>0?n.join(", "):"N/A"};return(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)(j.Z,{userID:null,userEmail:null,userRole:null,premiumUser:!1,setProxySettings:Y,proxySettings:W,accessToken:v||null,isPublicPage:!0}),(0,s.jsxs)("div",{className:"w-full px-8 py-12",children:[(0,s.jsxs)(l.Zb,{className:"mb-10 p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,s.jsx)(l.Dx,{className:"text-2xl font-semibold mb-6 text-gray-900",children:"About"}),(0,s.jsx)("p",{className:"text-gray-700 mb-6 text-base leading-relaxed",children:A||"Proxy Server to call 100+ LLMs in the OpenAI format."}),(0,s.jsx)("div",{className:"flex items-center space-x-3 text-sm text-gray-600",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"w-4 h-4 mr-2",children:"\uD83D\uDD27"}),"Built with litellm: v",k]})})]}),O&&Object.keys(O).length>0&&(0,s.jsxs)(l.Zb,{className:"mb-10 p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,s.jsx)(l.Dx,{className:"text-2xl font-semibold mb-6 text-gray-900",children:"Useful Links"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.entries(O||{}).map(e=>{let[t,n]=e;return(0,s.jsxs)("button",{onClick:()=>window.open(n,"_blank"),className:"flex items-center space-x-3 text-blue-600 hover:text-blue-800 transition-colors p-3 rounded-lg hover:bg-blue-50 border border-gray-200",children:[(0,s.jsx)(u.Z,{className:"w-4 h-4"}),(0,s.jsx)(l.xv,{className:"text-sm font-medium",children:t})]},t)})})]}),(0,s.jsxs)(l.Zb,{className:"mb-10 p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,s.jsx)(l.Dx,{className:"text-2xl font-semibold mb-6 text-gray-900",children:"Health and Endpoint Status"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,s.jsxs)(l.xv,{className:"text-green-600 font-medium text-sm",children:["Service status: ",K]})})]}),(0,s.jsxs)(l.Zb,{className:"p-8 bg-white border border-gray-200 rounded-lg shadow-sm",children:[(0,s.jsx)("div",{className:"flex justify-between items-center mb-8",children:(0,s.jsx)(l.Dx,{className:"text-2xl font-semibold text-gray-900",children:"Available Models"})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,s.jsx)(l.xv,{className:"text-sm font-medium text-gray-700",children:"Search Models:"}),(0,s.jsx)(c.Z,{title:"Smart search with relevance ranking - finds models containing your search terms, ranked by relevance. Try searching 'xai grok-4', 'claude-4', 'gpt-4', or 'sonnet'",placement:"top",children:(0,s.jsx)(x.Z,{className:"w-4 h-4 text-gray-400 cursor-help"})})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.Z,{className:"w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,s.jsx)("input",{type:"text",placeholder:"Search model names... (smart search enabled)",value:z,onChange:e=>D(e.target.value),className:"border border-gray-300 rounded-lg pl-10 pr-4 py-2 w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-sm font-medium mb-3 text-gray-700",children:"Provider:"}),(0,s.jsx)(m.default,{mode:"multiple",value:L,onChange:e=>P(e),placeholder:"Select providers",className:"w-full",size:"large",allowClear:!0,optionRender:e=>{let{logo:t}=(0,b.dr)(e.value);return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,s.jsx)("img",{src:t,alt:e.label,className:"w-5 h-5 flex-shrink-0 object-contain",onError:e=>{e.target.style.display="none"}}),(0,s.jsx)("span",{className:"capitalize",children:e.label})]})},children:y&&(e=>{let t=new Set;return e.forEach(e=>{e.providers.forEach(e=>t.add(e))}),Array.from(t)})(y).map(e=>(0,s.jsx)(m.default.Option,{value:e,children:e},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-sm font-medium mb-3 text-gray-700",children:"Mode:"}),(0,s.jsx)(m.default,{mode:"multiple",value:Z,onChange:e=>R(e),placeholder:"Select modes",className:"w-full",size:"large",allowClear:!0,children:y&&(e=>{let t=new Set;return e.forEach(e=>{e.mode&&t.add(e.mode)}),Array.from(t)})(y).map(e=>(0,s.jsx)(m.default.Option,{value:e,children:e},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-sm font-medium mb-3 text-gray-700",children:"Features:"}),(0,s.jsx)(m.default,{mode:"multiple",value:G,onChange:e=>H(e),placeholder:"Select features",className:"w-full",size:"large",allowClear:!0,children:y&&(e=>{let t=new Set;return e.forEach(e=>{Object.entries(e).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).forEach(e=>{let[n]=e,s=n.replace(/^supports_/,"").split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");t.add(s)})}),Array.from(t).sort()})(y).map(e=>(0,s.jsx)(m.default.Option,{value:e,children:e},e))})]})]}),(0,s.jsx)(i.C,{columns:[{header:"Model Name",accessorKey:"model_group",enableSorting:!0,cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"overflow-hidden",children:(0,s.jsx)(c.Z,{title:t.original.model_group,children:(0,s.jsx)(l.zx,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left",onClick:()=>X(t.original),children:t.original.model_group})})})},size:150},{header:"Providers",accessorKey:"providers",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.providers;return(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:n.map(e=>{let{logo:t}=(0,b.dr)(e);return(0,s.jsxs)("div",{className:"flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded text-xs",children:[t&&(0,s.jsx)("img",{src:t,alt:e,className:"w-3 h-3 flex-shrink-0 object-contain",onError:e=>{e.target.style.display="none"}}),(0,s.jsx)("span",{className:"capitalize",children:e})]},e)})})},size:120},{header:"Mode",accessorKey:"mode",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.mode;return(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:(e=>{switch(null==e?void 0:e.toLowerCase()){case"chat":return"\uD83D\uDCAC";case"rerank":return"\uD83D\uDD04";case"embedding":return"\uD83D\uDCC4";default:return"\uD83E\uDD16"}})(n||"")}),(0,s.jsx)(l.xv,{children:n||"Chat"})]})},size:100},{header:"Max Input",accessorKey:"max_input_tokens",enableSorting:!0,cell:e=>{let{row:t}=e;return(0,s.jsx)(l.xv,{className:"text-center",children:es(t.original.max_input_tokens)})},size:100,meta:{className:"text-center"}},{header:"Max Output",accessorKey:"max_output_tokens",enableSorting:!0,cell:e=>{let{row:t}=e;return(0,s.jsx)(l.xv,{className:"text-center",children:es(t.original.max_output_tokens)})},size:100,meta:{className:"text-center"}},{header:"Input $/1M",accessorKey:"input_cost_per_token",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.input_cost_per_token;return(0,s.jsx)(l.xv,{className:"text-center",children:n?en(n):"Free"})},size:100,meta:{className:"text-center"}},{header:"Output $/1M",accessorKey:"output_cost_per_token",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original.output_cost_per_token;return(0,s.jsx)(l.xv,{className:"text-center",children:n?en(n):"Free"})},size:100,meta:{className:"text-center"}},{header:"Features",accessorKey:"supports_vision",enableSorting:!1,cell:e=>{let{row:t}=e,n=Object.entries(t.original).filter(e=>{let[t,n]=e;return t.startsWith("supports_")&&!0===n}).map(e=>{let[t]=e;return ee(t)});return 0===n.length?(0,s.jsx)(l.xv,{className:"text-gray-400",children:"-"}):1===n.length?(0,s.jsx)("div",{className:"h-6 flex items-center",children:(0,s.jsx)(d.Z,{color:"blue",className:"text-xs",children:n[0]})}):(0,s.jsxs)("div",{className:"h-6 flex items-center space-x-1",children:[(0,s.jsx)(d.Z,{color:"blue",className:"text-xs",children:n[0]}),(0,s.jsx)(c.Z,{title:(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"font-medium",children:"All Features:"}),n.map((e,t)=>(0,s.jsxs)("div",{className:"text-xs",children:["• ",e]},t))]}),trigger:"click",placement:"topLeft",children:(0,s.jsxs)("span",{className:"text-xs text-blue-600 cursor-pointer hover:text-blue-800 hover:underline",onClick:e=>e.stopPropagation(),children:["+",n.length-1]})})]})},size:120},{header:"Limits",accessorKey:"rpm",enableSorting:!0,cell:e=>{let{row:t}=e,n=t.original;return(0,s.jsx)(l.xv,{className:"text-xs text-gray-600",children:ea(n.rpm,n.tpm)})},size:150}],data:$,isLoading:T,table:J,defaultSorting:[{id:"model_group",desc:!1}]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(l.xv,{className:"text-sm text-gray-600",children:["Showing ",$.length," of ",(null==y?void 0:y.length)||0," models"]})})]})]}),(0,s.jsx)(p.Z,{title:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:(null==B?void 0:B.model_group)||"Model Details"}),B&&(0,s.jsx)(c.Z,{title:"Copy model name",children:(0,s.jsx)(h.Z,{onClick:()=>Q(B.model_group),className:"cursor-pointer text-gray-500 hover:text-blue-500 w-4 h-4"})})]}),width:1e3,open:U,footer:null,onOk:()=>{q(!1),V(null)},onCancel:()=>{q(!1),V(null)},children:B&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-lg font-semibold mb-4",children:"Model Overview"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Model Name:"}),(0,s.jsx)(l.xv,{children:B.model_group})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Mode:"}),(0,s.jsx)(l.xv,{children:B.mode||"Not specified"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Providers:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:B.providers.map(e=>{let{logo:t}=(0,b.dr)(e);return(0,s.jsx)(d.Z,{color:"blue",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&(0,s.jsx)("img",{src:t,alt:e,className:"w-3 h-3 flex-shrink-0 object-contain",onError:e=>{e.target.style.display="none"}}),(0,s.jsx)("span",{className:"capitalize",children:e})]})},e)})})]})]}),B.model_group.includes("*")&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)(x.Z,{className:"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium text-blue-900 mb-2",children:"Wildcard Routing"}),(0,s.jsxs)(l.xv,{className:"text-sm text-blue-800 mb-2",children:["This model uses wildcard routing. You can pass any value where you see the ",(0,s.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded text-xs",children:"*"})," symbol."]}),(0,s.jsxs)(l.xv,{className:"text-sm text-blue-800",children:["For example, with ",(0,s.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded text-xs",children:B.model_group}),", you can use any string (",(0,s.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded text-xs",children:B.model_group.replace("*","my-custom-value")}),") that matches this pattern."]})]})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-lg font-semibold mb-4",children:"Token & Cost Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Max Input Tokens:"}),(0,s.jsx)(l.xv,{children:(null===(t=B.max_input_tokens)||void 0===t?void 0:t.toLocaleString())||"Not specified"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Max Output Tokens:"}),(0,s.jsx)(l.xv,{children:(null===(n=B.max_output_tokens)||void 0===n?void 0:n.toLocaleString())||"Not specified"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Input Cost per 1M Tokens:"}),(0,s.jsx)(l.xv,{children:B.input_cost_per_token?en(B.input_cost_per_token):"Not specified"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Output Cost per 1M Tokens:"}),(0,s.jsx)(l.xv,{children:B.output_cost_per_token?en(B.output_cost_per_token):"Not specified"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-lg font-semibold mb-4",children:"Capabilities"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{let e=et(B),t=["green","blue","purple","orange","red","yellow"];return 0===e.length?(0,s.jsx)(l.xv,{className:"text-gray-500",children:"No special capabilities listed"}):e.map((e,n)=>(0,s.jsx)(d.Z,{color:t[n%t.length],children:ee(e)},e))})()})]}),(B.tpm||B.rpm)&&(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-lg font-semibold mb-4",children:"Rate Limits"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[B.tpm&&(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Tokens per Minute:"}),(0,s.jsx)(l.xv,{children:B.tpm.toLocaleString()})]}),B.rpm&&(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"font-medium",children:"Requests per Minute:"}),(0,s.jsx)(l.xv,{children:B.rpm.toLocaleString()})]})]})]}),B.supported_openai_params&&(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-lg font-semibold mb-4",children:"Supported OpenAI Parameters"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:B.supported_openai_params.map(e=>(0,s.jsx)(d.Z,{color:"green",children:e},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.xv,{className:"text-lg font-semibold mb-4",children:"Usage Example"}),(0,s.jsx)("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto",children:(0,s.jsx)("pre",{className:"text-sm",children:(0,f.L)({apiKeySource:"custom",accessToken:null,apiKey:"your_api_key",inputMessage:"Hello, how are you?",chatHistory:[{role:"user",content:"Hello, how are you?",isImage:!1}],selectedTags:[],selectedVectorStores:[],selectedGuardrails:[],endpointType:(0,_.vf)(B.mode||"chat"),selectedModel:B.model_group,selectedSdk:"openai"})})}),(0,s.jsx)("div",{className:"mt-2 text-right",children:(0,s.jsx)("button",{onClick:()=>{Q((0,f.L)({apiKeySource:"custom",accessToken:null,apiKey:"your_api_key",inputMessage:"Hello, how are you?",chatHistory:[{role:"user",content:"Hello, how are you?",isImage:!1}],selectedTags:[],selectedVectorStores:[],selectedGuardrails:[],endpointType:(0,_.vf)(B.mode||"chat"),selectedModel:B.model_group,selectedSdk:"openai"}))},className:"text-sm text-blue-600 hover:text-blue-800 cursor-pointer",children:"Copy to clipboard"})})]})]})})]})}}}]);